# 改进版本 - 基于用户代码的优秀设计
# 保持简洁性的同时增强功能，满足1.1.2节完整需求

import datetime
import math
from typing import Dict, List, Any

class Args:
    def __init__(self, params: Dict[str, Any]):
        self.params = params

Output = Dict[str, Any]

async def main(args: Args) -> Output:
    """1.1.2节 数据质量验证体系 - 改进版本
    
    基于用户代码的优秀设计，保持简洁性的同时增强功能
    """
    # 获取输入数据
    input_data = args.params.get('outputList', [])
    if not input_data:
        return {
            "validation_results": [],
            "statistics": {
                "total_count": 0,
                "level_distribution": {"A": 0, "B": 0, "C": 0, "D": 0},
                "average_quality_score": 0.0,
                "quality_rate": 0.0
            },
            "algorithm_info": {
                "section": "1.1.2 数据质量验证体系",
                "model": "简化的二维质量评分模型",
                "status": "无数据输入"
            }
        }
    
    # 处理所有数据（而不只是最新记录）
    results = []
    level_counts = {"A": 0, "B": 0, "C": 0, "D": 0}
    total_quality_score = 0.0
    
    for i, data_item in enumerate(input_data):
        # 提取字段
        conversation = data_item.get('conversation', '')
        emo_value_str = data_item.get('emo_value', '5')
        timestamp = data_item.get('bstudio_create_time', '')
        number = data_item.get('number', '0')
        p25 = data_item.get('p25', data_item.get('P25', 4.0))
        p75 = data_item.get('p75', data_item.get('P75', 6.0))
        p50 = data_item.get('p50', data_item.get('P50', 5.0))
        
        # 类型转换安全处理
        try:
            emo_value = float(emo_value_str)
        except:
            emo_value = 5.0
        
        try:
            number_val = int(number)
        except:
            number_val = 0
        
        try:
            p25, p50, p75 = float(p25), float(p50), float(p75)
        except:
            p25, p50, p75 = 4.0, 5.0, 6.0
        
        # 数据完整性评分
        def calculate_completeness():
            score = 0.0
            
            # 核心字段完整性（S/M/T维度）
            required_fields = ['conversation', 'emo_value', 'bstudio_create_time']
            present_fields = sum(1 for f in required_fields if data_item.get(f) is not None and str(data_item.get(f)).strip())
            score += (present_fields / len(required_fields)) * 5.0  # 5分权重
            
            # 时间戳准确性
            try:
                datetime.datetime.strptime(timestamp[:19], "%Y-%m-%d %H:%M:%S")
                score += 2.0  # 时间格式正确
            except:
                pass  # 格式错误不加分
            
            # 内容丰富度（语义密度）
            content_length = len(conversation.strip())
            if content_length > 20:
                score += 2.0
            elif content_length > 5:
                score += 1.0
                
            # 上下文信息（number字段存在性）
            if number_val > 0:
                score += 1.0
                
            return min(10.0, score)
        
        # 行为一致性评分
        def calculate_consistency():
            score = 0.0
            
            # 情绪-表达一致性（40%）
            if 1 <= emo_value <= 9:
                # 根据内容长度调整情绪可信度
                content_length = len(conversation.strip())
                if content_length > 10:
                    score += 4.0  # 长内容更可信
                elif content_length > 3:
                    score += 3.0
                else:
                    score += 2.0
            
            # 时间模式一致性（30%）
            try:
                time_obj = datetime.datetime.strptime(timestamp[:19], "%Y-%m-%d %H:%M:%S")
                hour = time_obj.hour
                # 假设正常交互时间在8-20点
                if 8 <= hour <= 20:
                    score += 3.0
                else:
                    score += 2.0  # 非常规时间扣1分
            except:
                score += 1.5  # 时间格式错误得一半分
                
            # 个体基线一致性（30%）
            if p25 <= emo_value <= p75:
                score += 3.0
            elif abs(emo_value - p50) < 2:
                score += 2.0
            else:
                score += 1.0
                
            return min(10.0, score)
        
        # 质量问题诊断
        def diagnose_issues():
            issues = []
            
            # 检查数据完整性问题
            if not conversation.strip():
                issues.append("对话内容为空")
            if not timestamp:
                issues.append("时间戳缺失")
            
            # 检查一致性问题
            if emo_value < 1 or emo_value > 9:
                issues.append("情绪值超出正常范围")
            if abs(emo_value - p50) > 3:
                issues.append("情绪值严重偏离个体基线")
            
            # 检查可疑模式
            if len(set(conversation.lower())) < 3 and len(conversation) > 5:
                issues.append("检测到重复字符模式")
            
            test_patterns = ['test', '测试', 'aaa', '111', 'xxx']
            if any(pattern in conversation.lower() for pattern in test_patterns):
                issues.append("检测到测试文本模式")
            
            return issues
        
        # 计算基础评分
        completeness = calculate_completeness()
        consistency = calculate_consistency()
        
        # 综合质量评分
        quality_score = completeness * 0.6 + consistency * 0.4
        
        # 质量等级判定
        def determine_quality_grade(score):
            if score >= 8.0:
                return "A级（优质）", "直接使用", 1.0
            elif score >= 6.0:
                return "B级（良好）", "正常使用", 0.8
            elif score >= 4.0:
                return "C级（可用）", "谨慎使用", 0.5
            else:
                return "D级（异常）", "隔离审核", 0.1
        
        grade, strategy, weight = determine_quality_grade(quality_score)
        issues = diagnose_issues()
        
        # 构建单条记录结果
        result = {
            "data_index": i + 1,
            "quality_score": round(quality_score, 2),
            "completeness_score": round(completeness, 2),
            "consistency_score": round(consistency, 2),
            "quality_grade": grade,
            "weight_coefficient": weight,
            "handling_strategy": strategy,
            "issues": issues,
            "emotion_analysis": {
                "emotion_value": emo_value,
                "content_length": len(conversation.strip()),
                "baseline_range": f"P25:{p25}, P50:{p50}, P75:{p75}",
                "baseline_deviation": round(abs(emo_value - p50), 2)
            }
        }
        
        results.append(result)
        
        # 更新统计
        level = grade[0]  # 提取等级字母
        level_counts[level] += 1
        total_quality_score += quality_score
    
    # 计算统计信息
    total_count = len(results)
    average_quality = round(total_quality_score / total_count, 2) if total_count > 0 else 0.0
    quality_rate = round((level_counts["A"] + level_counts["B"]) / total_count * 100, 1) if total_count > 0 else 0.0
    
    # 返回完整结果
    return {
        "validation_results": results,
        "statistics": {
            "total_count": total_count,
            "level_distribution": level_counts,
            "level_percentages": {
                level: round(count / total_count * 100, 1) if total_count > 0 else 0.0
                for level, count in level_counts.items()
            },
            "average_quality_score": average_quality,
            "average_completeness": round(sum(r["completeness_score"] for r in results) / total_count, 2) if total_count > 0 else 0.0,
            "average_consistency": round(sum(r["consistency_score"] for r in results) / total_count, 2) if total_count > 0 else 0.0,
            "quality_rate": quality_rate,
            "usable_rate": round((level_counts["A"] + level_counts["B"] + level_counts["C"]) / total_count * 100, 1) if total_count > 0 else 0.0
        },
        "quality_thresholds": {
            "A_level": "8.0-10.0分，优先选取，权重系数1.0",
            "B_level": "6.0-7.9分，正常选取，权重系数0.8", 
            "C_level": "4.0-5.9分，容量不足时选取，权重系数0.5",
            "D_level": "0.0-3.9分，直接舍弃，权重系数0.1"
        },
        "algorithm_info": {
            "section": "1.1.2 数据质量验证体系",
            "model": "简化的二维质量评分模型",
            "theory_basis": "心理测量学信度理论",
            "completeness_weight": 0.6,
            "consistency_weight": 0.4,
            "evaluation_dimensions": {
                "completeness": ["核心字段完整性", "时间戳准确性", "内容丰富度", "上下文信息"],
                "consistency": ["情绪-表达一致性", "时间模式一致性", "个体基线一致性"]
            },
            "enhancements": [
                "批量数据处理",
                "详细问题诊断",
                "完整统计信息",
                "基于用户代码的简洁设计"
            ]
        },
        "processing_summary": {
            "total_processed": total_count,
            "processing_mode": "批量处理模式",
            "code_version": "改进版本 v1.0 - 基于用户优秀设计"
        }
    }

# 注意：此代码基于用户代码的优秀设计，保持简洁性的同时增强了功能
# 适用于Coze平台，支持批量处理和详细分析