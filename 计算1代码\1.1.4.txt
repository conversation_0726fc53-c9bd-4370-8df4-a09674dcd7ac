import numpy as np
from datetime import datetime
from typing import Any, Dict, List

async def main(args: Any) -> Dict[str, Any]:
    """
    多层次异常数据检测与处理系统
    """
    params = args.params
    output_list: List[Dict[str, Any]] = params['outputList']
    quality_grade: str = params['quality_grade']
    
    # 1. 数据预处理
    numbers = []
    emo_values = []
    times = []
    
    for item in output_list:
        try:
            # 数值型字段提取
            number = float(item['number'])
            emo_value = float(item['emo_value'])
            
            # 时间字段处理
            ts_str = item['bstudio_create_time'].split('+')[0].strip()
            dt = datetime.strptime(ts_str, "%Y-%m-%d %H:%M:%S")
            
        except (KeyError, ValueError):
            continue  # 跳过格式错误的数据
        else:
            numbers.append(number)
            emo_values.append(emo_value)
            times.append(dt)
    
    # 2. 统计异常检测
    def calculate_z_scores(data):
        if len(data) < 2: return []
        mean, std = np.mean(data), np.std(data)
        return [(abs(x - mean)/std) for x in data]
    
    z_scores_num = calculate_z_scores(numbers)
    z_scores_emo = calculate_z_scores(emo_values)
    
    # 3. IQR异常检测
    def detect_iqr(data):
        if len(data) < 4: return []
        q1, q3 = np.percentile(data, [25, 75])
        return [x for x in data if x < q1 - 1.5*(q3-q1) or x > q3 + 1.5*(q3-q1)]
    
    # 4. 时间合理性检测
    time_span = (max(times) - min(times)).days if times else 0
    hour_coverage = len(set(t.hour for t in times)) if times else 0
    
    # 5. 异常综合评估
    anomaly_metrics = {
        'zscore_count': sum(1 for z in z_scores_num + z_scores_emo if z > 2.5),
        'iqr_count': len(detect_iqr(numbers) + detect_iqr(emo_values)),
        'time_irregular': 1 if time_span > 30 and hour_coverage < 6 else 0,
        'quality_weight': {'A级': 0.8, 'B级': 1.0, 'C级': 1.2}.get(quality_grade[:2], 1.0)
    }
    
    # 6. 异常分数计算
    total_anomalies = (
        anomaly_metrics['zscore_count'] * 0.4 +
        anomaly_metrics['iqr_count'] * 0.3 +
        anomaly_metrics['time_irregular'] * 0.3
    ) * anomaly_metrics['quality_weight']
    
    # 7. 异常分级处理
    if total_anomalies > 5:
        severity = "严重异常"
        action = "自动隔离 ×0.1 权重"
    elif total_anomalies > 2:
        severity = "中度异常"
        action = "标记观察 ×0.3 权重"
    elif total_anomalies > 0:
        severity = "轻微异常"
        action = "自动修正 ×0.7 权重"
    else:
        severity = "正常数据"
        action = "继续监控"
    
    # 8. 返回结果
    return {
        "anomaly_score": f"{severity}（{total_anomalies:.2f}），处理策略：{action}",
        "metrics": {
            "zscore异常数": anomaly_metrics['zscore_count'],
            "IQR异常数": anomaly_metrics['iqr_count'],
            "时间跨度天数": time_span,
            "时段覆盖率": f"{hour_coverage}/24"
        }
    }