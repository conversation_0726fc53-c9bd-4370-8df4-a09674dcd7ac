"""
计算9：智能策略匹配系统
基于八维度并行评估模型的用户画像策略匹配
"""

import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

# 输入输出类型定义
class Args:
    def __init__(self, params: Dict[str, Any]):
        self.params = params

class Output:
    def __init__(self, data: Dict[str, Any]):
        self.data = data

    def __getitem__(self, key):
        return self.data[key]

    def __setitem__(self, key, value):
        self.data[key] = value

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，支持JSON序列化"""
        return self.data

class StrategyMatcher:
    """智能策略匹配核心引擎"""
    
    def __init__(self):
        # 用户类型策略权重配置
        self.user_type_weights = {
            '乐观开朗型': {'emotional': 0.3, 'social': 0.4, 'growth': 0.3},
            '悲观消极型': {'emotional': 0.5, 'social': 0.2, 'growth': 0.3},
            '沉稳内敛型': {'emotional': 0.2, 'social': 0.3, 'growth': 0.5},
            '情绪敏感型': {'emotional': 0.6, 'social': 0.2, 'growth': 0.2},
            '适应调整型': {'emotional': 0.3, 'social': 0.3, 'growth': 0.4}
        }
        
        # 策略库定义
        self.strategy_library = self._initialize_strategy_library()
        
        # 八维度权重配置
        self.dimension_weights = {
            'user_type': 0.15,      # 计算1
            'emotional_momentum': 0.12,  # 计算2
            'emotional_intensity': 0.10, # 计算3
            'relationship_stability': 0.18, # 计算4
            'emotional_inertia': 0.08,   # 计算5
            'crisis_assessment': 0.20,   # 计算6
            'health_assessment': 0.12,   # 计算7
            'trend_prediction': 0.05     # 计算8
        }
    
    def _initialize_strategy_library(self) -> Dict[str, Dict]:
        """初始化策略库 - 重新设计危机阈值和适用类型"""
        return {
            # 情绪调节策略
            'emotional_regulation': {
                'deep_breathing': {
                    'name': '深呼吸调节法',
                    'description': '通过深呼吸技巧缓解情绪压力',
                    'suitable_types': ['情绪敏感型', '悲观消极型', '适应调整型'],  # 扩展适用类型
                    'crisis_threshold': 0.7,  # 提高阈值，适用于中高风险
                    'effectiveness': 0.7
                },
                'positive_reframing': {
                    'name': '积极重构法',
                    'description': '重新解读负面事件，寻找积极意义',
                    'suitable_types': ['悲观消极型', '适应调整型', '乐观开朗型'],  # 扩展适用类型
                    'crisis_threshold': 0.6,  # 适用于中等风险
                    'effectiveness': 0.8
                },
                'mindfulness_meditation': {
                    'name': '正念冥想',
                    'description': '通过正念练习提升情绪觉察能力',
                    'suitable_types': ['情绪敏感型', '沉稳内敛型', '适应调整型'],  # 扩展适用类型
                    'crisis_threshold': 0.6,  # 提高阈值，适用于中等风险
                    'effectiveness': 0.75
                }
            },
            
            # 社交互动策略
            'social_interaction': {
                'active_listening': {
                    'name': '积极倾听技巧',
                    'description': '提升倾听质量，增强情感连接',
                    'suitable_types': ['乐观开朗型', '适应调整型', '沉稳内敛型'],  # 扩展适用类型
                    'crisis_threshold': 0.5,  # 提高阈值，适用于中等风险
                    'effectiveness': 0.8
                },
                'empathy_expression': {
                    'name': '共情表达法',
                    'description': '学会表达理解和共情，增进关系',
                    'suitable_types': ['沉稳内敛型', '情绪敏感型', '适应调整型'],  # 扩展适用类型
                    'crisis_threshold': 0.6,  # 提高阈值，适用于中等风险
                    'effectiveness': 0.75
                },
                'conflict_resolution': {
                    'name': '冲突解决技巧',
                    'description': '学习建设性地处理关系冲突',
                    'suitable_types': ['适应调整型', '乐观开朗型', '沉稳内敛型'],  # 扩展适用类型
                    'crisis_threshold': 0.7,  # 提高阈值，适用于中高风险
                    'effectiveness': 0.85
                }
            },
            
            # 个人成长策略
            'personal_growth': {
                'goal_setting': {
                    'name': '目标设定法',
                    'description': '制定清晰可行的个人发展目标',
                    'suitable_types': ['沉稳内敛型', '适应调整型', '乐观开朗型'],  # 扩展适用类型
                    'crisis_threshold': 0.5,  # 提高阈值，适用于中等风险
                    'effectiveness': 0.8
                },
                'self_reflection': {
                    'name': '自我反思练习',
                    'description': '定期进行自我反思，促进个人成长',
                    'suitable_types': ['沉稳内敛型', '情绪敏感型', '适应调整型'],  # 扩展适用类型
                    'crisis_threshold': 0.4,  # 提高阈值，适用于低中风险
                    'effectiveness': 0.7
                },
                'skill_development': {
                    'name': '技能提升计划',
                    'description': '制定并执行技能提升计划',
                    'suitable_types': ['乐观开朗型', '适应调整型', '沉稳内敛型'],  # 扩展适用类型
                    'crisis_threshold': 0.6,  # 提高阈值，适用于中等风险
                    'effectiveness': 0.75
                }
            },
            
            # 危机干预策略
            'crisis_intervention': {
                'immediate_support': {
                    'name': '即时支持方案',
                    'description': '提供即时的情感支持和安全感',
                    'suitable_types': ['情绪敏感型', '悲观消极型'],
                    'crisis_threshold': 0.8,
                    'effectiveness': 0.9
                },
                'professional_referral': {
                    'name': '专业转介建议',
                    'description': '建议寻求专业心理健康服务',
                    'suitable_types': ['悲观消极型', '情绪敏感型'],
                    'crisis_threshold': 0.9,
                    'effectiveness': 0.95
                },
                'safety_planning': {
                    'name': '安全计划制定',
                    'description': '制定详细的安全保障计划',
                    'suitable_types': ['乐观开朗型', '悲观消极型', '沉稳内敛型', '情绪敏感型', '适应调整型'],
                    'crisis_threshold': 0.85,
                    'effectiveness': 0.9
                }
            },
            
            # 关系维护策略
            'relationship_maintenance': {
                'regular_checkin': {
                    'name': '定期关系检视',
                    'description': '定期评估和维护关系质量',
                    'suitable_types': ['沉稳内敛型', '适应调整型', '乐观开朗型'],  # 扩展适用类型
                    'crisis_threshold': 0.6,  # 提高阈值，适用于中等风险
                    'effectiveness': 0.7
                },
                'appreciation_expression': {
                    'name': '感谢表达练习',
                    'description': '学会表达感谢和欣赏',
                    'suitable_types': ['乐观开朗型', '沉稳内敛型', '适应调整型'],  # 扩展适用类型
                    'crisis_threshold': 0.5,  # 提高阈值，适用于中等风险
                    'effectiveness': 0.8
                },
                'boundary_setting': {
                    'name': '边界设定技巧',
                    'description': '学会设定健康的关系边界',
                    'suitable_types': ['情绪敏感型', '适应调整型', '沉稳内敛型'],  # 扩展适用类型
                    'crisis_threshold': 0.7,  # 提高阈值，适用于中高风险
                    'effectiveness': 0.75
                }
            }
        }
    
    def calculate_posterior_baseline(self, params: Dict[str, Any]) -> Dict[str, float]:
        """计算后验基线"""
        # 安全获取并转换数值参数
        prior_p25 = float(params.get('final_P25', params.get('P25', 5.0)))
        prior_p50 = float(params.get('final_P50', params.get('P50', 7.0)))
        prior_p75 = float(params.get('final_P75', params.get('P75', 8.0)))
        prior_confidence = float(params.get('final_conf_score', params.get('confidence_level', 0.5)))

        # 观测数据（近期）
        cem_value = float(params.get('cem_value', 0.0))

        # 处理CEM等级映射
        cem_grade = params.get('cem_grade', 'C')
        cem_grade_mapping = {
            '基本稳定': 'B',
            '稳定': 'A',
            '不稳定': 'D',
            '波动': 'C',
            'A': 'A', 'B': 'B', 'C': 'C', 'D': 'D'
        }
        mapped_grade = cem_grade_mapping.get(cem_grade, 'C')
        cem_grade_confidence = {'A': 0.9, 'B': 0.7, 'C': 0.5, 'D': 0.3}.get(mapped_grade, 0.5)
        
        # 贝叶斯融合
        prior_weight = prior_confidence
        observation_weight = cem_grade_confidence
        total_weight = prior_weight + observation_weight
        
        if total_weight > 0:
            baseline_shift = cem_value * 0.3  # CEM动量转换为基线偏移
            
            posterior_p25 = (prior_p25 * prior_weight + (prior_p25 + baseline_shift) * observation_weight) / total_weight
            posterior_p50 = (prior_p50 * prior_weight + (prior_p50 + baseline_shift) * observation_weight) / total_weight
            posterior_p75 = (prior_p75 * prior_weight + (prior_p75 + baseline_shift) * observation_weight) / total_weight
            posterior_confidence = min(1.0, total_weight / 2.0)
        else:
            posterior_p25, posterior_p50, posterior_p75 = prior_p25, prior_p50, prior_p75
            posterior_confidence = prior_confidence
        
        return {
            'posterior_p25': posterior_p25,
            'posterior_p50': posterior_p50,
            'posterior_p75': posterior_p75,
            'posterior_confidence': posterior_confidence
        }
    
    def calculate_eight_dimension_scores(self, params: Dict[str, Any]) -> Dict[str, float]:
        """计算八维度评估分数"""
        scores = {}

        # 安全获取数值参数的辅助函数
        def safe_float(key: str, default: float = 0.0, fallback_keys: List[str] = None) -> float:
            """安全获取并转换浮点数参数"""
            # 首先尝试主键
            value = params.get(key)
            if value is not None:
                try:
                    return float(value)
                except (ValueError, TypeError):
                    pass

            # 尝试备用键
            if fallback_keys:
                for fallback_key in fallback_keys:
                    value = params.get(fallback_key)
                    if value is not None:
                        try:
                            return float(value)
                        except (ValueError, TypeError):
                            continue

            return default

        # 计算1：用户类型分类 - 映射final_conf_score到confidence_level
        scores['user_type_score'] = safe_float('final_conf_score', 0.5, ['confidence_level'])

        # 计算2：情绪动量
        cem_value = safe_float('cem_value', 0.0)
        cem_normalized = (cem_value + 2.0) / 4.0  # 归一化到[0,1]
        scores['emotional_momentum_score'] = max(0.0, min(1.0, cem_normalized))

        # 计算3：情绪强度
        ei_value = safe_float('ei_value', 0.0)
        scores['emotional_intensity_score'] = max(0.0, min(1.0, ei_value / 2.0))

        # 计算4：关系稳定性
        scores['relationship_stability_score'] = safe_float('rsi_value', 0.5)

        # 计算5：情绪惯性
        scores['emotional_inertia_score'] = safe_float('eii_value', 0.5)

        # 计算6：危机评估
        crisis_prob = safe_float('crisis_probability', 0.5)
        scores['crisis_assessment_score'] = max(0.0, min(1.0, 1.0 - crisis_prob))

        # 计算7：健康评估
        scores['health_assessment_score'] = safe_float('health_score', 0.5)

        # 计算8：趋势预测 - 处理复杂的trend_prediction结构
        trend_score = self._calculate_trend_score(params.get('trend_prediction'))
        scores['trend_prediction_score'] = trend_score

        return scores

    def _calculate_trend_score(self, trend_prediction: Any) -> float:
        """计算趋势预测分数，处理复杂的trend_prediction结构"""
        if trend_prediction is None:
            return 0.5  # 默认中性分数

        # 如果是字符串格式（旧格式兼容）
        if isinstance(trend_prediction, str):
            if trend_prediction == 'positive':
                return 0.7
            elif trend_prediction == 'negative':
                return 0.3
            else:
                return 0.5

        # 如果是字典格式（新格式）
        if isinstance(trend_prediction, dict):
            # 优先使用短期趋势
            short_term = trend_prediction.get('short_term_trend', {})
            medium_term = trend_prediction.get('medium_term_trend', {})

            # 获取趋势方向和置信度
            direction = short_term.get('direction', medium_term.get('direction', '保持稳定'))
            confidence = short_term.get('confidence', medium_term.get('confidence', 0.5))

            # 根据方向计算基础分数
            if '上升' in direction or '积极' in direction or 'positive' in direction.lower():
                base_score = 0.7
            elif '下降' in direction or '消极' in direction or 'negative' in direction.lower():
                base_score = 0.3
            else:  # 稳定或其他
                base_score = 0.5

            # 根据置信度调整分数
            try:
                confidence_float = float(confidence)
                # 置信度越高，分数越接近基础分数；置信度越低，分数越接近中性(0.5)
                adjusted_score = base_score * confidence_float + 0.5 * (1 - confidence_float)
                return max(0.0, min(1.0, adjusted_score))
            except (ValueError, TypeError):
                return base_score

        # 其他情况返回中性分数
        return 0.5

    def calculate_comprehensive_adaptability(self, params: Dict[str, Any], dimension_scores: Dict[str, float]) -> float:
        """计算综合适配度"""
        weighted_score = 0.0
        
        # 八维度加权求和
        score_mapping = {
            'user_type': dimension_scores['user_type_score'],
            'emotional_momentum': dimension_scores['emotional_momentum_score'],
            'emotional_intensity': dimension_scores['emotional_intensity_score'],
            'relationship_stability': dimension_scores['relationship_stability_score'],
            'emotional_inertia': dimension_scores['emotional_inertia_score'],
            'crisis_assessment': dimension_scores['crisis_assessment_score'],
            'health_assessment': dimension_scores['health_assessment_score'],
            'trend_prediction': dimension_scores['trend_prediction_score']
        }
        
        for dimension, weight in self.dimension_weights.items():
            weighted_score += score_mapping[dimension] * weight
        
        return min(1.0, max(0.0, weighted_score))

    def select_optimal_strategies(self, params: Dict[str, Any], adaptability_score: float) -> List[Dict[str, Any]]:
        """选择最优策略组合"""
        # 安全获取参数
        user_type = params.get('user_type', '适应调整型')
        crisis_prob = float(params.get('crisis_probability', 0.5))

        selected_strategies = []

        # 危机优先处理
        if crisis_prob > 0.7:
            for strategy_id, strategy_info in self.strategy_library['crisis_intervention'].items():
                crisis_threshold = float(strategy_info.get('crisis_threshold', 1.0))
                if crisis_prob >= crisis_threshold:
                    selected_strategies.append({
                        'category': 'crisis_intervention',
                        'strategy_id': strategy_id,
                        'strategy_name': strategy_info['name'],
                        'description': strategy_info['description'],
                        'priority': 'P0',
                        'effectiveness': float(strategy_info.get('effectiveness', 0.5)),
                        'adaptability_score': float(adaptability_score)
                    })

        # 基于用户类型选择策略
        user_weights = self.user_type_weights.get(user_type, {'emotional': 0.33, 'social': 0.33, 'growth': 0.34})

        # 情绪调节策略
        if user_weights['emotional'] >= 0.3:  # 修改为 >= 以包含边界值
            for strategy_id, strategy_info in self.strategy_library['emotional_regulation'].items():
                crisis_threshold = float(strategy_info.get('crisis_threshold', 1.0))
                # 修复逻辑：当危机概率不超过策略的最大适用阈值时选择策略
                if user_type in strategy_info['suitable_types'] and crisis_prob <= crisis_threshold:
                    selected_strategies.append({
                        'category': 'emotional_regulation',
                        'strategy_id': strategy_id,
                        'strategy_name': strategy_info['name'],
                        'description': strategy_info['description'],
                        'priority': 'P1',
                        'effectiveness': float(strategy_info.get('effectiveness', 0.5)),
                        'adaptability_score': float(adaptability_score) * float(user_weights['emotional'])
                    })

        # 社交互动策略
        if user_weights['social'] >= 0.3:  # 修改为 >= 以包含边界值
            for strategy_id, strategy_info in self.strategy_library['social_interaction'].items():
                crisis_threshold = float(strategy_info.get('crisis_threshold', 1.0))
                # 修复逻辑：当危机概率不超过策略的最大适用阈值时选择策略
                if user_type in strategy_info['suitable_types'] and crisis_prob <= crisis_threshold:
                    selected_strategies.append({
                        'category': 'social_interaction',
                        'strategy_id': strategy_id,
                        'strategy_name': strategy_info['name'],
                        'description': strategy_info['description'],
                        'priority': 'P2',
                        'effectiveness': float(strategy_info.get('effectiveness', 0.5)),
                        'adaptability_score': float(adaptability_score) * float(user_weights['social'])
                    })

        # 个人成长策略
        if user_weights['growth'] >= 0.3:  # 修改为 >= 以包含边界值
            for strategy_id, strategy_info in self.strategy_library['personal_growth'].items():
                crisis_threshold = float(strategy_info.get('crisis_threshold', 1.0))
                # 修复逻辑：当危机概率不超过策略的最大适用阈值时选择策略
                if user_type in strategy_info['suitable_types'] and crisis_prob <= crisis_threshold:
                    selected_strategies.append({
                        'category': 'personal_growth',
                        'strategy_id': strategy_id,
                        'strategy_name': strategy_info['name'],
                        'description': strategy_info['description'],
                        'priority': 'P2',
                        'effectiveness': float(strategy_info.get('effectiveness', 0.5)),
                        'adaptability_score': float(adaptability_score) * float(user_weights['growth'])
                    })

        # 关系维护策略
        rsi_value = float(params.get('rsi_value', 0.5))
        if rsi_value < 0.6:  # RSI较低时需要关系维护
            for strategy_id, strategy_info in self.strategy_library['relationship_maintenance'].items():
                crisis_threshold = float(strategy_info.get('crisis_threshold', 1.0))
                # 修复逻辑：当危机概率不超过策略的最大适用阈值时选择策略
                if user_type in strategy_info['suitable_types'] and crisis_prob <= crisis_threshold:
                    selected_strategies.append({
                        'category': 'relationship_maintenance',
                        'strategy_id': strategy_id,
                        'strategy_name': strategy_info['name'],
                        'description': strategy_info['description'],
                        'priority': 'P1',
                        'effectiveness': float(strategy_info.get('effectiveness', 0.5)),
                        'adaptability_score': float(adaptability_score) * 0.8
                    })

        # 按适配度排序并限制数量
        selected_strategies.sort(key=lambda x: x['adaptability_score'], reverse=True)
        # 按适配度排序并限制数量
        selected_strategies.sort(key=lambda x: x['adaptability_score'], reverse=True)
        return selected_strategies[:5]  # 最多返回5个策略

    def calculate_strategy_confidence(self, params: Dict[str, Any], strategies: List[Dict[str, Any]]) -> float:
        """计算策略置信度"""
        # 基于多个因素计算置信度 - 安全获取参数
        base_confidence = float(params.get('final_conf_score', params.get('confidence_level', 0.5)))
        ei_confidence = float(params.get('ei_confidence', 0.5))
        crisis_prob = float(params.get('crisis_probability', 0.5))
        crisis_certainty = 1.0 - abs(crisis_prob - 0.5) * 2  # 危机概率越接近0.5越不确定

        # 综合置信度
        overall_confidence = (base_confidence * 0.4 + ei_confidence * 0.3 + crisis_certainty * 0.3)

        # 策略数量调整
        strategy_count_factor = min(1.0, len(strategies) / 3.0)  # 策略数量适中时置信度更高

        return min(1.0, overall_confidence * strategy_count_factor)

    def generate_execution_plan(self, strategies: List[Dict[str, Any]], params: Dict[str, Any]) -> Dict[str, Any]:
        """生成执行计划"""
        # 按优先级分组
        p0_strategies = [s for s in strategies if s['priority'] == 'P0']
        p1_strategies = [s for s in strategies if s['priority'] == 'P1']
        p2_strategies = [s for s in strategies if s['priority'] == 'P2']

        # 基于趋势预测确定执行时机
        opportunity_windows = params.get('opportunity_windows', 'immediate')

        execution_timing = {
            'immediate': p0_strategies,
            'short_term': p1_strategies,
            'long_term': p2_strategies
        }

        return {
            'execution_sequence': execution_timing,
            'total_strategies': len(strategies),
            'crisis_strategies': len(p0_strategies),
            'primary_strategies': len(p1_strategies),
            'secondary_strategies': len(p2_strategies),
            'recommended_start_time': 'immediate' if p0_strategies else 'within_24_hours'
        }

async def main(args: Args) -> Dict[str, Any]:
    """
    计算9主函数：智能策略匹配系统
    """
    params = args.params

    # 初始化策略匹配器
    matcher = StrategyMatcher()

    try:
        # 1. 计算后验基线
        posterior_baseline = matcher.calculate_posterior_baseline(params)

        # 2. 计算八维度评估分数
        dimension_scores = matcher.calculate_eight_dimension_scores(params)

        # 3. 计算综合适配度
        adaptability_score = matcher.calculate_comprehensive_adaptability(params, dimension_scores)

        # 4. 选择最优策略
        optimal_strategies = matcher.select_optimal_strategies(params, adaptability_score)

        # 5. 计算策略置信度
        strategy_confidence = matcher.calculate_strategy_confidence(params, optimal_strategies)

        # 6. 生成执行计划
        execution_plan = matcher.generate_execution_plan(optimal_strategies, params)

        # 7. 构建输出结果
        ret = {
            # 核心输出
            "calculation_id": "calculation_9",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),

            # 后验基线结果
            "posterior_baseline": posterior_baseline,

            # 八维度评估结果
            "dimension_scores": dimension_scores,
            "comprehensive_adaptability": adaptability_score,

            # 策略匹配结果
            "selected_strategies": optimal_strategies,
            "strategy_confidence": strategy_confidence,
            "strategy_count": len(optimal_strategies),

            # 执行计划
            "execution_plan": execution_plan,

            # 质量控制信息
            "quality_metrics": {
                "input_completeness": _check_input_completeness(params),
                "parameter_validity": _validate_parameters(params),
                "confidence_level": strategy_confidence,
                "risk_assessment": _get_risk_assessment(params)
            },

            # 元数据
            "metadata": {
                "user_type": params.get('user_type', '适应调整型'),
                "crisis_probability": float(params.get('crisis_probability', 0.5)),
                "processing_time": "calculated_in_real_time",
                "algorithm_version": "eight_dimension_parallel_evaluation_v1.0"
            }
        }

        return ret

    except Exception as e:
        # 错误处理
        error_ret = {
            "calculation_id": "calculation_9",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "error": True,
            "error_message": str(e),
            "error_type": type(e).__name__,
            "fallback_strategies": _get_fallback_strategies(params.get('user_type', '适应调整型')),
            "quality_metrics": {
                "input_completeness": _check_input_completeness(params),
                "parameter_validity": False,
                "confidence_level": 0.0,
                "risk_assessment": "unknown"
            }
        }
        return error_ret

def _get_risk_assessment(params: Dict[str, Any]) -> str:
    """获取风险评估等级"""
    try:
        crisis_prob = float(params.get('crisis_probability', 0.5))
        if crisis_prob > 0.7:
            return "high"
        elif crisis_prob > 0.4:
            return "medium"
        else:
            return "low"
    except (ValueError, TypeError):
        return "unknown"

def _check_input_completeness(params: Dict[str, Any]) -> float:
    """检查输入参数完整性"""
    # 更新必需参数列表，包含备用参数名
    required_params_with_fallbacks = [
        ('user_type', []),
        ('final_conf_score', ['confidence_level']),
        ('final_P25', ['P25']),
        ('final_P50', ['P50']),
        ('final_P75', ['P75']),
        ('cem_value', []),
        ('cem_grade', []),
        ('ei_value', []),
        ('ei_confidence', []),
        ('eii_value', []),
        ('crisis_probability', []),
        ('risk_level', []),
        ('rsi_value', []),
        ('health_score', [])
    ]

    present_count = 0
    for main_param, fallback_params in required_params_with_fallbacks:
        # 检查主参数
        if main_param in params and params[main_param] is not None:
            present_count += 1
        else:
            # 检查备用参数
            found_fallback = False
            for fallback in fallback_params:
                if fallback in params and params[fallback] is not None:
                    present_count += 1
                    found_fallback = True
                    break
            # 如果没有备用参数但主参数存在，也算作存在
            if not found_fallback and not fallback_params and main_param in params:
                present_count += 1

    return present_count / len(required_params_with_fallbacks)

def _validate_parameters(params: Dict[str, Any]) -> bool:
    """验证参数有效性"""
    try:
        # 安全获取并验证关键参数范围
        def safe_validate_range(key: str, min_val: float, max_val: float, fallback_keys: List[str] = None) -> bool:
            """安全验证参数范围"""
            value = params.get(key)
            if value is None and fallback_keys:
                for fallback_key in fallback_keys:
                    value = params.get(fallback_key)
                    if value is not None:
                        break

            if value is None:
                return True  # 缺失参数不算验证失败

            try:
                float_value = float(value)
                return min_val <= float_value <= max_val
            except (ValueError, TypeError):
                return False

        # 验证各个参数范围
        validations = [
            safe_validate_range('final_conf_score', 0.0, 1.0, ['confidence_level']),
            safe_validate_range('cem_value', -2.0, 2.0),
            safe_validate_range('ei_value', 0.0, 2.0),
            safe_validate_range('crisis_probability', 0.0, 1.0),
            safe_validate_range('rsi_value', 0.0, 1.0),
            safe_validate_range('eii_value', 0.0, 1.0),
            safe_validate_range('health_score', 0.0, 1.0),
            safe_validate_range('ei_confidence', 0.0, 1.0)
        ]

        return all(validations)
    except Exception:
        return False

def _get_fallback_strategies(user_type: str) -> List[Dict[str, Any]]:
    """获取降级策略"""
    fallback = {
        '乐观开朗型': [{'name': '保持积极心态', 'description': '继续发挥乐观优势'}],
        '悲观消极型': [{'name': '寻求专业帮助', 'description': '建议咨询心理健康专家'}],
        '沉稳内敛型': [{'name': '自我反思', 'description': '进行深度自我反思'}],
        '情绪敏感型': [{'name': '情绪调节', 'description': '使用基础情绪调节技巧'}],
        '适应调整型': [{'name': '灵活应对', 'description': '保持适应性和灵活性'}]
    }
    return fallback.get(user_type, [{'name': '基础支持', 'description': '寻求基础情感支持'}])

# 测试函数（可选）
def test_calculation_9():
    """测试计算9功能"""
    test_params = {
        'user_type': '情绪敏感型',
        'final_conf_score': 0.8,
        'final_P25': 3.5,
        'final_P50': 5.0,
        'final_P75': 6.5,
        'cem_value': -0.5,
        'cem_grade': 'B',
        'ei_value': 1.2,
        'ei_confidence': 0.7,
        'eii_value': 0.6,
        'crisis_probability': 0.3,
        'risk_level': 'medium',
        'rsi_value': 0.4,
        'coordination_index': 0.5,
        'adaptability_score': 0.6,
        'health_score': 0.7,
        'trend_prediction': 'stable',
        'opportunity_windows': 'short_term'
    }

    args = Args(test_params)
    import asyncio
    result = asyncio.run(main(args))
    return result
