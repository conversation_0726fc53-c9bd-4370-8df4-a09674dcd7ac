#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1.1.6 数据选取流程集成与边缘情况处理 - 配置文件

包含所有相关的配置参数、阈值设置和系统常量

作者：AI助手
创建时间：2025年1月
"""

from dataclasses import dataclass
from typing import Dict, Any
from enum import Enum

class SystemMode(Enum):
    """系统运行模式"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"

@dataclass
class DataQualityConfig:
    """数据质量配置"""
    # DQS阈值设置
    dqs_threshold: float = 4.0
    
    # 质量等级阈值
    quality_thresholds: Dict[str, float] = None
    
    # 质量评估权重
    quality_weights: Dict[str, float] = None
    
    def __post_init__(self):
        if self.quality_thresholds is None:
            self.quality_thresholds = {
                'A': 7.0,  # 优质数据
                'B': 5.5,  # 良好数据
                'C': 4.0,  # 可用数据
                'D': 0.0   # 不合格数据
            }
        
        if self.quality_weights is None:
            self.quality_weights = {
                'completeness': 0.6,    # 完整性权重
                'consistency': 0.4      # 一致性权重
            }

@dataclass
class AnomalyDetectionConfig:
    """异常检测配置"""
    # Z-score阈值
    z_score_thresholds: Dict[str, float] = None
    
    # 权重调整因子
    weight_factors: Dict[str, float] = None
    
    # 最小历史数据量
    min_historical_data: int = 5
    
    # 默认异常检测阈值
    default_emotion_range: Dict[str, float] = None
    
    def __post_init__(self):
        if self.z_score_thresholds is None:
            self.z_score_thresholds = {
                'severe': 3.0,     # 严重异常
                'moderate': 2.5,   # 中度异常
                'light': 1.5,      # 轻微异常
                'normal': 0.0      # 正常
            }
        
        if self.weight_factors is None:
            self.weight_factors = {
                'normal': 1.0,     # 正常数据权重
                'light': 0.7,      # 轻微异常权重
                'moderate': 0.3,   # 中度异常权重
                'severe': 0.0      # 严重异常权重（隔离）
            }
        
        if self.default_emotion_range is None:
            self.default_emotion_range = {
                'min_normal': 2.0,
                'max_normal': 8.0,
                'min_acceptable': 1.0,
                'max_acceptable': 9.0
            }

@dataclass
class DataSufficiencyConfig:
    """数据充分性配置"""
    # DSI阈值
    dsi_threshold: float = 0.4
    
    # DSI计算权重
    dsi_weights: Dict[str, float] = None
    
    # 充分性评估基准
    sufficiency_benchmarks: Dict[str, float] = None
    
    def __post_init__(self):
        if self.dsi_weights is None:
            self.dsi_weights = {
                'data_volume': 0.4,     # 数据量权重
                'time_span': 0.3,       # 时间跨度权重
                'time_coverage': 0.15,  # 时段覆盖权重
                'quality_score': 0.15   # 质量分数权重
            }
        
        if self.sufficiency_benchmarks is None:
            self.sufficiency_benchmarks = {
                'optimal_data_count': 50,    # 最优数据量
                'optimal_time_span': 30,     # 最优时间跨度（天）
                'min_data_count': 10,        # 最小数据量
                'min_time_span': 7           # 最小时间跨度（天）
            }

@dataclass
class LayeredStorageConfig:
    """分层存储配置"""
    # 各层时间范围（天）
    layer_time_ranges: Dict[str, int] = None
    
    # 各层容量限制
    layer_capacities: Dict[str, int] = None
    
    # 各层权重系数
    layer_weights: Dict[str, float] = None
    
    def __post_init__(self):
        if self.layer_time_ranges is None:
            self.layer_time_ranges = {
                'working_memory': 7,      # 工作记忆：0-7天
                'short_term_memory': 28,  # 短期记忆：8-28天
                'long_term_memory': 112,  # 长期记忆：29-112天
                'core_memory': 999999     # 核心记忆：>112天
            }
        
        if self.layer_capacities is None:
            self.layer_capacities = {
                'working_memory': 50,     # 工作记忆容量
                'short_term_memory': 150, # 短期记忆容量
                'long_term_memory': 300,  # 长期记忆容量
                'core_memory': 100        # 核心记忆容量
            }
        
        if self.layer_weights is None:
            self.layer_weights = {
                'working_memory': 1.0,    # 工作记忆权重
                'short_term_memory': 0.8, # 短期记忆权重
                'long_term_memory': 0.6,  # 长期记忆权重
                'core_memory': 0.4        # 核心记忆权重
            }

@dataclass
class FeatureImportanceConfig:
    """特征重要性配置"""
    # 重要性评估权重
    importance_weights: Dict[str, float] = None
    
    # 文本长度基准
    text_length_benchmark: int = 100
    
    # 情绪中性基线
    emotion_neutral_baseline: float = 5.0
    
    def __post_init__(self):
        if self.importance_weights is None:
            self.importance_weights = {
                'quality_weight': 0.4,    # 质量权重
                'emotion_weight': 0.3,    # 情绪权重
                'length_weight': 0.2,     # 长度权重
                'factor_weight': 0.1      # 调整因子权重
            }

@dataclass
class EdgeCaseConfig:
    """边缘情况配置"""
    # 冷启动阈值
    cold_start_thresholds: Dict[str, int] = None
    
    # 稀疏表达阈值
    sparse_expression_thresholds: Dict[str, float] = None
    
    # 情绪单一化阈值
    emotion_monotony_threshold: float = 1.0
    
    # 低质量持续阈值
    low_quality_thresholds: Dict[str, float] = None
    
    # 边缘情况调整参数
    edge_case_adjustments: Dict[str, Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.cold_start_thresholds is None:
            self.cold_start_thresholds = {
                'data_count': 20,         # 数据量阈值
                'time_span_days': 14,     # 时间跨度阈值
                'reeval_frequency': 10    # 重新评估频率
            }
        
        if self.sparse_expression_thresholds is None:
            self.sparse_expression_thresholds = {
                'weekly_frequency': 2.0,      # 每周频率阈值
                'collection_weeks': 10,       # 收集周期（周）
                'time_coverage_min': 0.4,     # 最小时段覆盖
                'extreme_weight_boost': 1.5   # 极值权重提升
            }
        
        if self.low_quality_thresholds is None:
            self.low_quality_thresholds = {
                'avg_quality_threshold': 4.0,  # 平均质量阈值
                'min_data_count': 10           # 最小数据量
            }
        
        if self.edge_case_adjustments is None:
            self.edge_case_adjustments = {
                'cold_start': {
                    'dsi_threshold': 0.3,
                    'baseline_emotion': 5.0,
                    'data_collection_priority': True,
                    'min_quality_level': 'B'
                },
                'sparse_expression': {
                    'collection_period_weeks': 10,
                    'time_coverage_requirement': 0.4,
                    'extreme_value_weight': 1.5,
                    'anomaly_threshold': 3.0
                },
                'emotion_monotony': {
                    'use_relative_deviation': True,
                    'word_time_weight_boost': 1.3,
                    'observation_period_extend': True,
                    'micro_pattern_detection': True
                },
                'low_quality_persistent': {
                    'reevaluate_collection': True,
                    'personalized_quality_weights': True,
                    'manual_annotation_required': True,
                    'special_user_flag': True
                }
            }

@dataclass
class DatabaseConfig:
    """数据库配置"""
    # 数据库文件路径
    db_path: str = "data_selection_integration.db"
    
    # 连接池配置
    connection_pool_size: int = 10
    
    # 查询超时时间（秒）
    query_timeout: int = 30
    
    # 批量操作大小
    batch_size: int = 100
    
    # 数据保留期（天）
    data_retention_days: int = 365
    
    # 索引配置
    index_config: Dict[str, list] = None
    
    def __post_init__(self):
        if self.index_config is None:
            self.index_config = {
                'data_records': [
                    ['user_id', 'timestamp'],
                    ['processing_status'],
                    ['quality_level'],
                    ['anomaly_level']
                ],
                'user_profiles': [
                    ['user_id'],
                    ['is_cold_start'],
                    ['last_update']
                ],
                'isolated_data': [
                    ['user_id'],
                    ['review_status'],
                    ['isolation_time']
                ]
            }

@dataclass
class PerformanceConfig:
    """性能配置"""
    # 并发处理线程数
    max_workers: int = 4
    
    # 缓存大小
    cache_size: int = 1000
    
    # 缓存过期时间（秒）
    cache_ttl: int = 3600
    
    # 批量处理大小
    batch_processing_size: int = 50
    
    # 处理超时时间（秒）
    processing_timeout: int = 60
    
    # 内存使用限制（MB）
    memory_limit_mb: int = 512
    
    # 统计更新间隔（秒）
    stats_update_interval: int = 300

@dataclass
class LoggingConfig:
    """日志配置"""
    # 日志级别
    log_level: str = "INFO"
    
    # 日志文件路径
    log_file_path: str = "data_selection_integration.log"
    
    # 日志文件最大大小（MB）
    max_file_size_mb: int = 10
    
    # 日志文件备份数量
    backup_count: int = 5
    
    # 日志格式
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 是否输出到控制台
    console_output: bool = True
    
    # 敏感信息过滤
    filter_sensitive_data: bool = True

class DataSelectionConfig:
    """数据选取流程集成配置管理器"""
    
    def __init__(self, mode: SystemMode = SystemMode.DEVELOPMENT):
        self.mode = mode
        
        # 初始化各模块配置
        self.data_quality = DataQualityConfig()
        self.anomaly_detection = AnomalyDetectionConfig()
        self.data_sufficiency = DataSufficiencyConfig()
        self.layered_storage = LayeredStorageConfig()
        self.feature_importance = FeatureImportanceConfig()
        self.edge_case = EdgeCaseConfig()
        self.database = DatabaseConfig()
        self.performance = PerformanceConfig()
        self.logging = LoggingConfig()
        
        # 根据运行模式调整配置
        self._adjust_config_by_mode()
    
    def _adjust_config_by_mode(self):
        """根据运行模式调整配置"""
        if self.mode == SystemMode.DEVELOPMENT:
            # 开发模式：更详细的日志，更小的批量大小
            self.logging.log_level = "DEBUG"
            self.performance.batch_processing_size = 10
            self.database.db_path = "dev_data_selection_integration.db"
            
        elif self.mode == SystemMode.TESTING:
            # 测试模式：更严格的阈值，更快的处理
            self.data_quality.dqs_threshold = 5.0
            self.data_sufficiency.dsi_threshold = 0.5
            self.performance.processing_timeout = 30
            self.database.db_path = "test_data_selection_integration.db"
            
        elif self.mode == SystemMode.PRODUCTION:
            # 生产模式：优化性能，更大的缓存
            self.logging.log_level = "WARNING"
            self.performance.cache_size = 5000
            self.performance.max_workers = 8
            self.database.connection_pool_size = 20
            self.database.db_path = "prod_data_selection_integration.db"
    
    def get_config_dict(self) -> Dict[str, Any]:
        """获取完整配置字典"""
        return {
            'mode': self.mode.value,
            'data_quality': self.data_quality.__dict__,
            'anomaly_detection': self.anomaly_detection.__dict__,
            'data_sufficiency': self.data_sufficiency.__dict__,
            'layered_storage': self.layered_storage.__dict__,
            'feature_importance': self.feature_importance.__dict__,
            'edge_case': self.edge_case.__dict__,
            'database': self.database.__dict__,
            'performance': self.performance.__dict__,
            'logging': self.logging.__dict__
        }
    
    def update_config(self, config_dict: Dict[str, Any]):
        """更新配置"""
        for section, values in config_dict.items():
            if hasattr(self, section) and isinstance(values, dict):
                config_obj = getattr(self, section)
                for key, value in values.items():
                    if hasattr(config_obj, key):
                        setattr(config_obj, key, value)
    
    def validate_config(self) -> Dict[str, list]:
        """验证配置有效性"""
        errors = []
        warnings = []
        
        # 验证数据质量配置
        if self.data_quality.dqs_threshold < 0 or self.data_quality.dqs_threshold > 10:
            errors.append("DQS阈值必须在0-10之间")
        
        # 验证异常检测配置
        z_thresholds = self.anomaly_detection.z_score_thresholds
        if z_thresholds['light'] >= z_thresholds['moderate']:
            errors.append("轻微异常阈值必须小于中度异常阈值")
        
        # 验证数据充分性配置
        dsi_weights = self.data_sufficiency.dsi_weights
        if abs(sum(dsi_weights.values()) - 1.0) > 0.01:
            errors.append("DSI权重总和必须等于1.0")
        
        # 验证性能配置
        if self.performance.max_workers < 1:
            errors.append("最大工作线程数必须大于0")
        
        if self.performance.cache_size < 100:
            warnings.append("缓存大小较小，可能影响性能")
        
        # 验证边缘情况配置
        if self.edge_case.cold_start_thresholds['data_count'] < 5:
            warnings.append("冷启动数据量阈值过小，可能影响判断准确性")
        
        return {
            'errors': errors,
            'warnings': warnings
        }
    
    def export_config(self, file_path: str):
        """导出配置到文件"""
        import json
        
        config_dict = self.get_config_dict()
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    def import_config(self, file_path: str):
        """从文件导入配置"""
        import json
        
        with open(file_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        
        self.update_config(config_dict)
    
    def get_edge_case_adjustment(self, case_type: str) -> Dict[str, Any]:
        """获取特定边缘情况的调整参数"""
        return self.edge_case.edge_case_adjustments.get(case_type, {})
    
    def get_quality_threshold(self, level: str) -> float:
        """获取质量等级阈值"""
        return self.data_quality.quality_thresholds.get(level, 0.0)
    
    def get_anomaly_weight_factor(self, level: str) -> float:
        """获取异常等级权重因子"""
        return self.anomaly_detection.weight_factors.get(level, 1.0)
    
    def get_layer_capacity(self, layer: str) -> int:
        """获取存储层容量"""
        return self.layered_storage.layer_capacities.get(layer, 100)
    
    def get_layer_time_range(self, layer: str) -> int:
        """获取存储层时间范围"""
        return self.layered_storage.layer_time_ranges.get(layer, 30)

# 预定义配置实例
DEVELOPMENT_CONFIG = DataSelectionConfig(SystemMode.DEVELOPMENT)
TESTING_CONFIG = DataSelectionConfig(SystemMode.TESTING)
PRODUCTION_CONFIG = DataSelectionConfig(SystemMode.PRODUCTION)

# 配置验证和使用示例
if __name__ == "__main__":
    print("=== 1.1.6 数据选取流程集成配置测试 ===")
    
    # 测试开发模式配置
    dev_config = DataSelectionConfig(SystemMode.DEVELOPMENT)
    print(f"\n开发模式配置:")
    print(f"  - 数据库路径: {dev_config.database.db_path}")
    print(f"  - 日志级别: {dev_config.logging.log_level}")
    print(f"  - DQS阈值: {dev_config.data_quality.dqs_threshold}")
    print(f"  - 批量处理大小: {dev_config.performance.batch_processing_size}")
    
    # 测试生产模式配置
    prod_config = DataSelectionConfig(SystemMode.PRODUCTION)
    print(f"\n生产模式配置:")
    print(f"  - 数据库路径: {prod_config.database.db_path}")
    print(f"  - 日志级别: {prod_config.logging.log_level}")
    print(f"  - 缓存大小: {prod_config.performance.cache_size}")
    print(f"  - 最大工作线程: {prod_config.performance.max_workers}")
    
    # 配置验证
    print(f"\n配置验证结果:")
    validation_result = dev_config.validate_config()
    if validation_result['errors']:
        print(f"  错误: {validation_result['errors']}")
    if validation_result['warnings']:
        print(f"  警告: {validation_result['warnings']}")
    if not validation_result['errors'] and not validation_result['warnings']:
        print(f"  配置验证通过")
    
    # 测试边缘情况配置获取
    print(f"\n边缘情况配置:")
    cold_start_config = dev_config.get_edge_case_adjustment('cold_start')
    print(f"  冷启动调整: {cold_start_config}")
    
    sparse_config = dev_config.get_edge_case_adjustment('sparse_expression')
    print(f"  稀疏表达调整: {sparse_config}")
    
    # 测试质量阈值获取
    print(f"\n质量等级阈值:")
    for level in ['A', 'B', 'C', 'D']:
        threshold = dev_config.get_quality_threshold(level)
        print(f"  {level}级: {threshold}")
    
    # 测试异常权重因子
    print(f"\n异常等级权重因子:")
    for level in ['normal', 'light', 'moderate', 'severe']:
        weight = dev_config.get_anomaly_weight_factor(level)
        print(f"  {level}: {weight}")
    
    # 测试存储层配置
    print(f"\n存储层配置:")
    for layer in ['working_memory', 'short_term_memory', 'long_term_memory', 'core_memory']:
        capacity = dev_config.get_layer_capacity(layer)
        time_range = dev_config.get_layer_time_range(layer)
        print(f"  {layer}: 容量={capacity}, 时间范围={time_range}天")
    
    # 测试配置导出
    print(f"\n配置导出测试:")
    try:
        dev_config.export_config("test_config.json")
        print(f"  配置已导出到 test_config.json")
        
        # 测试配置导入
        new_config = DataSelectionConfig()
        new_config.import_config("test_config.json")
        print(f"  配置导入成功")
        
        # 清理测试文件
        import os
        if os.path.exists("test_config.json"):
            os.remove("test_config.json")
            print(f"  测试文件已清理")
            
    except Exception as e:
        print(f"  配置导入导出测试失败: {str(e)}")
    
    print("\n=== 配置测试完成 ===")