#!/usr/bin/env python3
"""
测试计算9：智能策略匹配系统
使用用户提供的真实输入数据进行测试
"""

import json
import asyncio
from calculation_9_strategy_matching import main, <PERSON>rgs

def test_with_user_input():
    """使用用户提供的输入数据进行测试"""
    
    # 用户提供的输入数据
    test_input = {
        "P25": "5.6",
        "P50": "7.6", 
        "P75": "7.8",
        "adaptability_score": "0.83",
        "cem_confidence": "0.553",
        "cem_grade": "基本稳定",
        "cem_value": "-0.226",
        "confidence_level": "0.804",
        "coordination_index": "0.305",
        "crisis_probability": "0.417",
        "ei_confidence": "0.858",
        "ei_value": "0.733",
        "eii_value": "0.46",
        "final_P25": "5.20",
        "final_P50": "8.53",
        "final_P75": "8.60",
        "final_conf_score": "0.13",
        "health_score": "0.69",
        "opportunity_windows": [],
        "risk_level": "中风险",
        "rsi_value": "0.476",
        "stability_trend": "稳定下降",
        "trend_prediction": {
            "medium_term_trend": {
                "confidence": 0.807,
                "direction": "保持稳定",
                "magnitude": 0.08,
                "time_horizon": "3-12个月"
            },
            "short_term_trend": {
                "confidence": 0.949,
                "direction": "保持稳定", 
                "magnitude": 0.1,
                "time_horizon": "1-3个月"
            }
        },
        "user_type": "适应调整型"
    }
    
    print("=== 计算9智能策略匹配系统测试 ===")
    print(f"输入数据: {json.dumps(test_input, ensure_ascii=False, indent=2)}")
    print("\n" + "="*50)
    
    try:
        # 创建Args对象
        args = Args(test_input)
        
        # 执行计算
        result = asyncio.run(main(args))
        
        print("✅ 测试成功！")
        print(f"输出结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 验证关键输出
        print("\n" + "="*50)
        print("=== 关键结果验证 ===")
        
        if result.get('error'):
            print(f"❌ 执行出错: {result.get('error_message')}")
            return False
        
        # 检查是否有策略输出
        strategies = result.get('selected_strategies', [])
        print(f"✅ 策略数量: {len(strategies)}")
        
        if strategies:
            print("✅ 策略详情:")
            for i, strategy in enumerate(strategies, 1):
                print(f"  {i}. {strategy.get('strategy_name', 'Unknown')} - {strategy.get('description', 'No description')}")
                print(f"     优先级: {strategy.get('priority', 'Unknown')}, 有效性: {strategy.get('effectiveness', 'Unknown')}")
        else:
            print("❌ 没有找到策略推荐")
            
        # 检查置信度
        confidence = result.get('strategy_confidence', 0)
        print(f"✅ 策略置信度: {confidence:.3f}")
        
        # 检查质量指标
        quality = result.get('quality_metrics', {})
        print(f"✅ 输入完整性: {quality.get('input_completeness', 0):.3f}")
        print(f"✅ 参数有效性: {quality.get('parameter_validity', False)}")
        print(f"✅ 风险评估: {quality.get('risk_assessment', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "="*50)
    print("=== 边界情况测试 ===")
    
    # 测试最小输入
    minimal_input = {
        "user_type": "适应调整型",
        "crisis_probability": "0.5"
    }
    
    print("测试最小输入...")
    try:
        args = Args(minimal_input)
        result = asyncio.run(main(args))
        
        if result.get('error'):
            print(f"⚠️  最小输入测试: {result.get('error_message')}")
        else:
            print("✅ 最小输入测试通过")
            
    except Exception as e:
        print(f"❌ 最小输入测试失败: {str(e)}")

if __name__ == "__main__":
    # 运行测试
    success = test_with_user_input()
    test_edge_cases()
    
    if success:
        print("\n🎉 所有测试完成！代码修复成功。")
    else:
        print("\n❌ 测试失败，需要进一步调试。")
