from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Any, Tu<PERSON>, Optional
from datetime import datetime
import numpy as np
import statistics
import math
import logging

logger = logging.getLogger(__name__)

class QualityGrade(Enum):
    A_EXCELLENT = "A级（优质）"
    B_GOOD = "B级（良好）"
    C_USABLE = "C级（可用）"
    D_ABNORMAL = "D级（异常）"

class MemoryLayer(Enum):
    CORE = "core_memory"
    EXTENDED = "extended_memory"
    EPISODIC = "episodic_memory"
    WORKING = "working_memory"
    CACHE = "cache_memory"

class AnomalyLevel(Enum):
    NORMAL = "normal"
    MILD = "mild"
    MODERATE = "moderate"
    SEVERE = "severe"

@dataclass
class EmotionRecord:
    user_id: str
    emo_value: float
    content: str
    timestamp: datetime
    context_tags: List[str]
    interaction_intensity: float

@dataclass
class PersonalTrait:
    trait_type: str
    content: str
    confidence: float
    timestamp: datetime

class DataQualityValidator:
    def __init__(self):
        self.quality_weights = {'completeness': 0.6, 'consistency': 0.4}
        self.grade_thresholds = {
            QualityGrade.A_EXCELLENT: 8.0,
            QualityGrade.B_GOOD: 6.0,
            QualityGrade.C_USABLE: 4.0,
            QualityGrade.D_ABNORMAL: 0.0
        }
    
    def calculate_quality_score(self, record: EmotionRecord) -> float:
        completeness = self._calculate_completeness_score(record)
        consistency = self._calculate_consistency_score(record)
        return completeness * 0.6 + consistency * 0.4
    
    def _calculate_completeness_score(self, record: EmotionRecord) -> float:
        score = 0.0
        if record.timestamp: score += 2.0
        if record.emo_value is not None and -1 <= record.emo_value <= 1: score += 3.0
        if record.content and len(record.content.strip()) > 0: score += 3.0
        if record.user_id: score += 1.0
        if record.context_tags: score += 1.0
        return score
    
    def _calculate_consistency_score(self, record: EmotionRecord) -> float:
        score = 10.0
        if record.emo_value is not None and not (-1 <= record.emo_value <= 1):
            score -= 3.0
        if record.content:
            clen = len(record.content)
            if clen < 5: score -= 2.0
            elif clen > 1000: score -= 1.0
        if record.timestamp:
            now = datetime.now()
            if record.timestamp > now: score -= 2.0
            elif (now - record.timestamp).days > 365: score -= 1.0
        return max(0.0, score)
    
    def determine_quality_grade(self, quality_score: float) -> Tuple[QualityGrade, str]:
        if quality_score >= 8.0: return QualityGrade.A_EXCELLENT, "直接使用"
        elif quality_score >= 6.0: return QualityGrade.B_GOOD, "正常使用"
        elif quality_score >= 4.0: return QualityGrade.C_USABLE, "谨慎使用"
        else: return QualityGrade.D_ABNORMAL, "需要清洗或丢弃"

class DataSufficiencyEvaluator:
    def __init__(self):
        self.min_records = {'high': 100, 'medium': 50, 'low': 20}
    
    def calculate_sufficiency_index(self, records: List[EmotionRecord]) -> float:
        if not records: return 0.0
        return min(1.0, len(records) / self.min_records['high'])
    
    def calculate_sufficiency_threshold(self, quality_scores: List[float]) -> float:
        if not quality_scores: return 0.5
        avg_quality = sum(quality_scores) / len(quality_scores)
        return 0.3 + (avg_quality * 0.4)
    
    def get_sufficiency_decision(self, sufficiency_index: float, threshold: float) -> Tuple[bool, str]:
        if sufficiency_index >= threshold:
            return True, "数据充分"
        else:
            return False, "数据不足"

class AnomalyDetector:
    def __init__(self):
        self.feature_means = None
        self.feature_stds = None
        self.is_fitted = False
        self.contamination = 0.1
    
    def fit(self, records: List[EmotionRecord]) -> None:
        if len(records) < 10: return
        features = self._extract_features(records)
        if features.size > 0:
            self.feature_means = np.mean(features, axis=0)
            self.feature_stds = np.std(features, axis=0)
            # 避免除零错误
            self.feature_stds = np.where(self.feature_stds == 0, 1, self.feature_stds)
            self.is_fitted = True
    
    def detect_anomaly(self, record: EmotionRecord) -> Tuple[AnomalyLevel, float, str]:
        if not self.is_fitted:
            return AnomalyLevel.NORMAL, 0.0, "正常使用"
        
        features = self._extract_features([record])
        if features.size == 0:
            return AnomalyLevel.NORMAL, 0.0, "正常使用"
        
        # 使用Z-score方法检测异常
        normalized_features = (features[0] - self.feature_means) / self.feature_stds
        # 计算马哈拉诺比斯距离的简化版本（欧几里得距离）
        anomaly_score = np.sqrt(np.sum(normalized_features ** 2))
        # 将距离转换为0-1范围的异常度
        anomaly_degree = min(1.0, anomaly_score / 5.0)  # 5是经验阈值
        
        if anomaly_degree > 0.8: return AnomalyLevel.SEVERE, anomaly_degree, "隔离处理"
        elif anomaly_degree > 0.6: return AnomalyLevel.MODERATE, anomaly_degree, "标记审核"
        elif anomaly_degree > 0.3: return AnomalyLevel.MILD, anomaly_degree, "降权处理"
        else: return AnomalyLevel.NORMAL, anomaly_degree, "正常使用"
    
    def _extract_features(self, records: List[EmotionRecord]) -> np.ndarray:
        features = []
        for record in records:
            f = [record.emo_value or 0.0, len(record.content or ""), 
                 record.timestamp.hour if record.timestamp else 12,
                 len(record.context_tags or []), record.interaction_intensity]
            features.append(f)
        return np.array(features)
    
    def _classify_anomaly_level(self, score: float) -> AnomalyLevel:
        if score >= 0.8: return AnomalyLevel.SEVERE
        elif score >= 0.6: return AnomalyLevel.MODERATE
        elif score >= 0.3: return AnomalyLevel.MILD
        else: return AnomalyLevel.NORMAL
    
    def get_processing_strategy(self, anomaly_level: AnomalyLevel) -> Tuple[str, float, str, bool]:
        strategies = {
            AnomalyLevel.NORMAL: ("正常处理", 1.0, "直接使用", False),
            AnomalyLevel.MILD: ("降权处理", 0.8, "降低权重使用", False),
            AnomalyLevel.MODERATE: ("标记审核", 0.5, "人工审核后使用", True),
            AnomalyLevel.SEVERE: ("隔离处理", 0.1, "隔离不使用", True)
        }
        return strategies.get(anomaly_level, strategies[AnomalyLevel.NORMAL])

class LayeredMemoryManager:
    def __init__(self):
        self.layer_capacities = {
            MemoryLayer.CORE: 50,
            MemoryLayer.EXTENDED: 200,
            MemoryLayer.EPISODIC: 500,
            MemoryLayer.WORKING: 100,
            MemoryLayer.CACHE: 1000
        }
        self.memory_storage = {layer: [] for layer in MemoryLayer}
    
    def add_record(self, record: EmotionRecord, quality_score: float) -> bool:
        target_layer = self._determine_target_layer(quality_score)
        self.memory_storage[target_layer].append((record, quality_score))
        self._manage_capacity(target_layer)
        return True
    
    def _determine_target_layer(self, quality_score: float) -> MemoryLayer:
        if quality_score >= 0.9: return MemoryLayer.CORE
        elif quality_score >= 0.7: return MemoryLayer.EXTENDED
        elif quality_score >= 0.5: return MemoryLayer.EPISODIC
        elif quality_score >= 0.3: return MemoryLayer.WORKING
        else: return MemoryLayer.CACHE
    
    def _manage_capacity(self, layer: MemoryLayer) -> None:
        capacity = self.layer_capacities[layer]
        storage = self.memory_storage[layer]
        if len(storage) > capacity:
            storage.sort(key=lambda x: x[1], reverse=True)
            self.memory_storage[layer] = storage[:capacity]
    
    def get_recent_records(self, count: int) -> List[EmotionRecord]:
        all_records = []
        for layer_records in self.memory_storage.values():
            all_records.extend([record for record, _ in layer_records])
        return all_records[-count:] if all_records else []

class PersonalTraitExtractor:
    def __init__(self):
        self.min_records = 10
    
    def extract_traits_from_records(self, records: List[EmotionRecord]) -> PersonalTrait:
        if len(records) < self.min_records:
            return PersonalTrait("insufficient_data", "数据不足", 0.0, datetime.now())
        
        comm = self._extract_communication_style(records)
        emo = self._extract_emotional_pattern(records)
        confidence = min(1.0, len(records) / 100.0)
        
        return PersonalTrait("综合个性特征", f"沟通:{comm:.2f}, 情绪:{emo:.2f}", confidence, datetime.now())
    
    def _extract_communication_style(self, records: List[EmotionRecord]) -> float:
        if not records: return 0.5
        total_length = sum(len(r.content) if r.content else 0 for r in records)
        avg_length = total_length / len(records)
        return min(1.0, avg_length / 200.0)
    
    def _extract_emotional_pattern(self, records: List[EmotionRecord]) -> float:
        if not records: return 0.5
        emotion_values = [r.emo_value for r in records if r.emo_value is not None]
        if not emotion_values: return 0.5
        avg_emotion = sum(emotion_values) / len(emotion_values)
        return max(0.0, min(1.0, (avg_emotion + 1) / 2))

class DataQualityValidationSystem:
    def __init__(self):
        self.quality_validator = DataQualityValidator()
        self.sufficiency_evaluator = DataSufficiencyEvaluator()
        self.anomaly_detector = AnomalyDetector()
        self.memory_manager = LayeredMemoryManager()
        self.trait_extractor = PersonalTraitExtractor()
        self.is_initialized = False
        self.total_processed = 0
    
    def initialize_system(self, historical_records: List[EmotionRecord] = None) -> bool:
        try:
            if historical_records:
                self.anomaly_detector.fit(historical_records)
                for record in historical_records:
                    quality_score = self.quality_validator.calculate_quality_score(record)
                    if quality_score >= 0.6:
                        self.memory_manager.add_record(record, quality_score)
            self.is_initialized = True
            return True
        except Exception as e:
            return False
    
    def process_new_record(self, record: EmotionRecord) -> Dict[str, Any]:
        try:
            quality_score = self.quality_validator.calculate_quality_score(record)
            quality_grade, processing_strategy = self.quality_validator.determine_quality_grade(quality_score)
            anomaly_level, anomaly_score, processing_suggestion = self.anomaly_detector.detect_anomaly(record)
            
            storage_decision = {'should_store': quality_grade in [QualityGrade.A_EXCELLENT, QualityGrade.B_GOOD] or 
                               (quality_grade == QualityGrade.C_USABLE and anomaly_level == AnomalyLevel.NORMAL)}
            
            if storage_decision['should_store']:
                self.memory_manager.add_record(record, quality_score)
            
            self.total_processed += 1
            
            return {
                'quality_score': quality_score,
                'quality_grade': quality_grade.value,
                'processing_strategy': processing_strategy,
                'anomaly_level': anomaly_level.value,
                'storage_decision': storage_decision
            }
        except Exception as e:
            return {'error': str(e)}
    
    def process_emotion_record(self, record: EmotionRecord) -> Dict[str, Any]:
        quality_score = self.quality_validator.calculate_quality_score(record)
        quality_grade, processing_strategy = self.quality_validator.determine_quality_grade(quality_score)
        storage_status = "stored" if quality_grade != QualityGrade.D_ABNORMAL else "rejected"
        return {
            'quality_score': quality_score, 
            'quality_grade': quality_grade.value,
            'processing_strategy': processing_strategy,
            'storage_status': storage_status
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        return {'initialized': self.is_initialized, 'total_processed': self.total_processed}

if __name__ == "__main__":
    system = DataQualityValidationSystem()
    sample_record = EmotionRecord(
        user_id="user_001",
        emo_value=0.7,
        content="今天心情不错，工作很顺利",
        timestamp=datetime.now(),
        context_tags=["工作", "积极"],
        interaction_intensity=0.8
    )
    result = system.process_new_record(sample_record)
    print("处理结果:", result)
    status = system.get_system_status()
    print("系统状态:", status)