#!/usr/bin/env python3
"""
调试趋势预测计算
"""

from calculation_9_strategy_matching import Strategy<PERSON>atch<PERSON>

def debug_trend_calculation():
    """调试趋势预测计算过程"""
    
    # 测试数据
    trend_prediction = {
        "medium_term_trend": {
            "confidence": 0.807,
            "direction": "保持稳定",
            "magnitude": 0.08,
            "time_horizon": "3-12个月"
        },
        "short_term_trend": {
            "confidence": 0.949,
            "direction": "保持稳定", 
            "magnitude": 0.1,
            "time_horizon": "1-3个月"
        }
    }
    
    print("=== 趋势预测计算调试 ===")
    print(f"输入数据: {trend_prediction}")
    
    matcher = StrategyMatcher()
    
    # 调试计算过程
    print("\n计算过程分析:")
    
    # 检查数据类型
    print(f"1. 数据类型: {type(trend_prediction)}")
    print(f"2. 是否为字典: {isinstance(trend_prediction, dict)}")
    
    if isinstance(trend_prediction, dict):
        short_term = trend_prediction.get('short_term_trend', {})
        medium_term = trend_prediction.get('medium_term_trend', {})
        
        print(f"3. 短期趋势数据: {short_term}")
        print(f"4. 中期趋势数据: {medium_term}")
        
        # 选择有效的趋势数据
        active_trend = short_term if short_term else medium_term
        print(f"5. 选择的活跃趋势: {active_trend}")
        
        if active_trend:
            direction = active_trend.get('direction', '保持稳定')
            confidence = active_trend.get('confidence', 0.5)
            magnitude = active_trend.get('magnitude', 0.0)
            
            print(f"6. 方向: {direction}")
            print(f"7. 置信度: {confidence}")
            print(f"8. 幅度: {magnitude}")
            
            # 计算基础分数
            if '上升' in direction or '积极' in direction or 'positive' in direction.lower():
                base_score = 0.6 + min(0.3, float(magnitude) * 3)
                print(f"9. 上升趋势，基础分数: {base_score}")
            elif '下降' in direction or '消极' in direction or 'negative' in direction.lower():
                base_score = 0.4 - min(0.3, float(magnitude) * 3)
                print(f"9. 下降趋势，基础分数: {base_score}")
            else:
                base_score = 0.5
                print(f"9. 稳定趋势，基础分数: {base_score}")
            
            # 置信度调整
            try:
                confidence_float = float(confidence)
                adjusted_score = base_score * confidence_float + 0.5 * (1 - confidence_float)
                final_score = max(0.0, min(1.0, adjusted_score))
                
                print(f"10. 置信度调整: {base_score} * {confidence_float} + 0.5 * {1 - confidence_float} = {adjusted_score}")
                print(f"11. 最终分数: {final_score}")
                
            except (ValueError, TypeError) as e:
                print(f"10. 置信度转换错误: {e}")
                final_score = base_score
    
    # 调用实际方法
    actual_score = matcher._calculate_trend_score(trend_prediction)
    print(f"\n实际计算结果: {actual_score}")
    
    # 分析为什么返回0.5
    if actual_score == 0.5:
        print("\n❌ 返回了默认值0.5，可能的原因:")
        print("1. 趋势方向被识别为'稳定'")
        print("2. 计算过程中出现异常")
        print("3. 逻辑分支有问题")
    else:
        print(f"\n✅ 计算正常，返回值: {actual_score}")

if __name__ == "__main__":
    debug_trend_calculation()
