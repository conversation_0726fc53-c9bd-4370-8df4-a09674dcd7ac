#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
情绪类型判定模拟验证系统
基于三参数智能关系管理系统v6.0的算法实现
验证15条对话数据是否能准确判定用户情绪类型
"""

import numpy as np
import statistics
from typing import List, Dict, Tuple
import random

class EmotionTypeClassifier:
    """情绪类型分类器"""
    
    def __init__(self):
        # 主导阈值定义（来自文档表格）
        self.thresholds = {
            'volatile': 0.75,      # 波动主导阈值
            'emotional': 0.80,     # 情绪倾向主导阈值（乐观/悲观）
            'stable': 0.85         # 稳定主导阈值
        }
        
        # 先验基线定义（来自文档）
        self.prior_baselines = {
            'optimistic': {'P25': 6.5, 'P50': 7.5, 'P75': 8.5},
            'pessimistic': {'P25': 2.5, 'P50': 3.5, 'P75': 4.5},
            'volatile': {'P25': 3.0, 'P50': 5.5, 'P75': 8.0},
            'stable': {'P25': 5.0, 'P50': 6.0, 'P75': 7.0},
            'unknown': {'P25': 5.0, 'P50': 5.5, 'P75': 6.0}  # 默认基线
        }
    
    def calculate_basic_metrics(self, scores: List[float]) -> Dict[str, float]:
        """计算基础统计指标"""
        if len(scores) < 3:
            raise ValueError("数据量不足，至少需要3条数据")
        
        # 数据预处理：确保在1-10范围内
        valid_scores = [s for s in scores if 1 <= s <= 10]
        if len(valid_scores) < 3:
            raise ValueError("有效数据不足3条")
        
        mean_score = statistics.mean(valid_scores)
        score_range = max(valid_scores) - min(valid_scores)
        std_dev = statistics.stdev(valid_scores) if len(valid_scores) > 1 else 0
        
        return {
            'mean_score': mean_score,
            'score_range': score_range,
            'std_dev': std_dev,
            'data_count': len(valid_scores)
        }
    
    def calculate_type_scores(self, metrics: Dict[str, float]) -> Dict[str, float]:
        """计算各用户类型得分（基于文档公式）"""
        mean_score = metrics['mean_score']
        score_range = metrics['score_range']
        std_dev = metrics['std_dev']
        
        scores = {}
        
        # 乐观型得分计算
        if mean_score >= 7:
            scores['optimistic'] = 0.8 + min(0.2, (mean_score - 7) * 0.1)
        else:
            scores['optimistic'] = 0.0
        
        # 悲观型得分计算
        if mean_score <= 4:
            scores['pessimistic'] = 0.8 + min(0.2, (4 - mean_score) * 0.1)
        else:
            scores['pessimistic'] = 0.0
        
        # 波动型得分计算
        if score_range >= 4:
            scores['volatile'] = 0.6 + min(0.3, (score_range - 4) * 0.1)
        else:
            scores['volatile'] = 0.0
        
        # 稳定型得分计算
        if std_dev <= 1.0:
            scores['stable'] = 0.7 + min(0.2, (1.0 - std_dev) * 0.2)
        else:
            scores['stable'] = 0.0
        
        return scores
    
    def determine_user_type(self, type_scores: Dict[str, float], metrics: Dict[str, float]) -> Tuple[str, float]:
        """根据优先级判断流程确定用户类型"""
        score_range = metrics['score_range']
        
        # 1. 波动型优先
        if score_range >= 5 and type_scores['volatile'] > self.thresholds['volatile']:
            return 'volatile', type_scores['volatile']
        
        # 2. 情绪倾向优先（乐观/悲观）
        if type_scores['optimistic'] > self.thresholds['emotional']:
            return 'optimistic', type_scores['optimistic']
        
        if type_scores['pessimistic'] > self.thresholds['emotional']:
            return 'pessimistic', type_scores['pessimistic']
        
        # 3. 稳定型优先（需要排除其他主导特征）
        if (type_scores['stable'] > self.thresholds['stable'] and 
            type_scores['volatile'] <= self.thresholds['volatile'] and
            type_scores['optimistic'] <= self.thresholds['emotional'] and
            type_scores['pessimistic'] <= self.thresholds['emotional']):
            return 'stable', type_scores['stable']
        
        # 4. 待观察（所有得分都低于阈值）
        return 'unknown', 0.5
    
    def classify_emotion_type(self, scores: List[float]) -> Dict[str, any]:
        """完整的情绪类型分类流程"""
        try:
            # 计算基础指标
            metrics = self.calculate_basic_metrics(scores)
            
            # 计算类型得分
            type_scores = self.calculate_type_scores(metrics)
            
            # 确定用户类型
            user_type, confidence = self.determine_user_type(type_scores, metrics)
            
            return {
                'user_type': user_type,
                'confidence': confidence,
                'metrics': metrics,
                'type_scores': type_scores,
                'prior_baseline': self.prior_baselines[user_type]
            }
        
        except Exception as e:
            return {
                'user_type': 'unknown',
                'confidence': 0.3,
                'error': str(e),
                'prior_baseline': self.prior_baselines['unknown']
            }

def generate_realistic_scenarios() -> Dict[str, List[float]]:
    """生成各种真实对话场景的情绪数据"""
    scenarios = {}
    
    # 1. 恋爱初期 - 甜蜜发展（乐观型）
    scenarios['恋爱初期_甜蜜发展'] = [7.2, 7.8, 7.5, 8.1, 8.3, 7.9, 8.5, 8.7, 8.2, 8.9, 9.1, 8.6, 9.0, 9.2, 9.0]
    
    # 2. 关系危机 - 逐步恶化（悲观型）
    scenarios['关系危机_逐步恶化'] = [6.5, 5.8, 5.2, 4.6, 4.1, 3.7, 3.2, 2.8, 2.5, 2.1, 1.8, 2.0, 1.5, 1.8, 1.6]
    
    # 3. 情绪波动 - 不稳定关系（波动型）
    scenarios['情绪波动_不稳定关系'] = [3.2, 8.7, 2.1, 9.3, 1.8, 8.9, 3.5, 9.1, 2.7, 8.4, 4.1, 7.8, 3.3, 8.6, 4.2]
    
    # 4. 平稳关系 - 稳定维持（稳定型）
    scenarios['平稳关系_稳定维持'] = [6.2, 6.0, 5.8, 6.1, 5.9, 6.3, 6.0, 5.7, 6.2, 5.9, 6.1, 6.0, 5.8, 6.2, 6.0]
    
    # 5. 工作压力期 - 中度悲观
    scenarios['工作压力期_中度悲观'] = [4.5, 4.2, 3.8, 4.0, 3.6, 4.1, 3.9, 3.7, 4.2, 3.8, 4.0, 3.5, 3.9, 4.1, 3.7]
    
    # 6. 考试冲刺期 - 高压波动
    scenarios['考试冲刺期_高压波动'] = [7.2, 3.5, 8.1, 2.8, 7.8, 4.2, 8.5, 3.1, 7.6, 3.8, 8.2, 4.0, 7.9, 3.6, 8.0]
    
    # 7. 新工作适应期 - 轻微乐观
    scenarios['新工作适应期_轻微乐观'] = [7.1, 7.3, 6.9, 7.5, 7.2, 7.0, 7.4, 7.1, 7.3, 6.8, 7.2, 7.0, 7.3, 7.1, 7.2]
    
    # 8. 长期抑郁状态 - 持续悲观
    scenarios['长期抑郁状态_持续悲观'] = [2.8, 2.5, 2.9, 2.3, 2.7, 2.4, 2.6, 2.8, 2.2, 2.5, 2.7, 2.4, 2.6, 2.3, 2.5]
    
    # 9. 退休生活 - 平和稳定
    scenarios['退休生活_平和稳定'] = [5.8, 5.9, 5.7, 6.0, 5.8, 5.9, 5.7, 6.1, 5.8, 5.9, 5.8, 6.0, 5.7, 5.9, 5.8]
    
    # 10. 创业初期 - 极度波动
    scenarios['创业初期_极度波动'] = [9.2, 1.5, 8.8, 2.1, 9.5, 1.8, 8.6, 2.4, 9.1, 1.9, 8.9, 2.2, 9.3, 1.6, 8.7]
    
    return scenarios

def run_simulation():
    """运行完整的模拟验证"""
    classifier = EmotionTypeClassifier()
    scenarios = generate_realistic_scenarios()
    
    print("=" * 80)
    print("情绪类型判定模拟验证系统")
    print("基于15条对话数据的准确性验证")
    print("=" * 80)
    
    results = []
    
    for scenario_name, emotion_scores in scenarios.items():
        print(f"\n📊 场景：{scenario_name}")
        print(f"情绪数据：{emotion_scores}")
        
        # 进行分类
        result = classifier.classify_emotion_type(emotion_scores)
        
        # 显示结果
        print(f"\n🎯 分析结果：")
        print(f"  判定类型：{result['user_type']}")
        print(f"  置信度：{result['confidence']:.3f}")
        
        if 'metrics' in result:
            metrics = result['metrics']
            print(f"\n📈 基础指标：")
            print(f"  平均分：{metrics['mean_score']:.2f}")
            print(f"  分数范围：{metrics['score_range']:.2f}")
            print(f"  标准差：{metrics['std_dev']:.3f}")
            print(f"  数据量：{metrics['data_count']}条")
            
            type_scores = result['type_scores']
            print(f"\n🔢 类型得分：")
            print(f"  乐观型：{type_scores['optimistic']:.3f}")
            print(f"  悲观型：{type_scores['pessimistic']:.3f}")
            print(f"  波动型：{type_scores['volatile']:.3f}")
            print(f"  稳定型：{type_scores['stable']:.3f}")
        
        baseline = result['prior_baseline']
        print(f"\n📏 先验基线：")
        print(f"  P25: {baseline['P25']}, P50: {baseline['P50']}, P75: {baseline['P75']}")
        
        results.append({
            'scenario': scenario_name,
            'result': result
        })
        
        print("-" * 60)
    
    # 统计分析
    print(f"\n📊 总体统计分析：")
    print(f"总场景数：{len(results)}")
    
    type_distribution = {}
    confidence_scores = []
    
    for r in results:
        user_type = r['result']['user_type']
        confidence = r['result']['confidence']
        
        type_distribution[user_type] = type_distribution.get(user_type, 0) + 1
        confidence_scores.append(confidence)
    
    print(f"\n🏷️ 类型分布：")
    for type_name, count in type_distribution.items():
        percentage = (count / len(results)) * 100
        print(f"  {type_name}: {count}个场景 ({percentage:.1f}%)")
    
    avg_confidence = statistics.mean(confidence_scores)
    print(f"\n🎯 平均置信度：{avg_confidence:.3f}")
    print(f"置信度范围：{min(confidence_scores):.3f} - {max(confidence_scores):.3f}")
    
    # 准确性评估
    print(f"\n✅ 准确性评估：")
    expected_types = {
        '恋爱初期_甜蜜发展': 'optimistic',
        '关系危机_逐步恶化': 'pessimistic', 
        '情绪波动_不稳定关系': 'volatile',
        '平稳关系_稳定维持': 'stable',
        '工作压力期_中度悲观': 'pessimistic',
        '考试冲刺期_高压波动': 'volatile',
        '新工作适应期_轻微乐观': 'optimistic',
        '长期抑郁状态_持续悲观': 'pessimistic',
        '退休生活_平和稳定': 'stable',
        '创业初期_极度波动': 'volatile'
    }
    
    correct_predictions = 0
    for r in results:
        scenario = r['scenario']
        predicted = r['result']['user_type']
        expected = expected_types.get(scenario, 'unknown')
        
        is_correct = predicted == expected
        if is_correct:
            correct_predictions += 1
        
        status = "✅" if is_correct else "❌"
        print(f"  {status} {scenario}: 预期={expected}, 实际={predicted}")
    
    accuracy = (correct_predictions / len(results)) * 100
    print(f"\n🎯 总体准确率：{accuracy:.1f}% ({correct_predictions}/{len(results)})")
    
    # 结论
    print(f"\n" + "=" * 80)
    print(f"📋 验证结论：")
    print(f"\n1. 数据量充足性：15条数据能够提供足够的统计信息")
    print(f"2. 分类准确性：{accuracy:.1f}%的准确率表明算法性能良好")
    print(f"3. 置信度水平：平均置信度{avg_confidence:.3f}，系统判断相对可靠")
    print(f"4. 类型覆盖性：成功识别了{len(type_distribution)}种不同的情绪类型")
    
    if accuracy >= 80:
        print(f"\n✅ 结论：15条对话数据能够准确判定用户情绪类型")
    elif accuracy >= 60:
        print(f"\n⚠️ 结论：15条对话数据基本能够判定用户情绪类型，但仍有改进空间")
    else:
        print(f"\n❌ 结论：15条对话数据不足以准确判定用户情绪类型，需要更多数据或算法优化")
    
    print("=" * 80)

if __name__ == "__main__":
    run_simulation()