#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1.1.6 数据选取流程集成与边缘情况处理

基于文档要求实现完整的数据选取流程集成系统，包括：
1. 完整数据选取流程串联
2. 不合格数据处理策略
3. 边缘情况应对策略
4. 流程质量控制与监控

作者：AI助手
创建时间：2025年1月
"""

import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
from dataclasses import dataclass, asdict
import numpy as np
from collections import defaultdict
import threading
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataQualityLevel(Enum):
    """数据质量等级"""
    A = "A"  # 优质数据 (DQS >= 7.0)
    B = "B"  # 良好数据 (DQS >= 5.5)
    C = "C"  # 可用数据 (DQS >= 4.0)
    D = "D"  # 不合格数据 (DQS < 4.0)

class AnomalyLevel(Enum):
    """异常程度等级"""
    NORMAL = "normal"        # 正常数据
    LIGHT = "light"          # 轻微异常
    MODERATE = "moderate"    # 中度异常
    SEVERE = "severe"        # 严重异常

class ProcessingStatus(Enum):
    """数据处理状态"""
    ACCEPTED = "accepted"          # 接受处理
    REJECTED = "rejected"          # 直接舍弃
    ISOLATED = "isolated"          # 隔离审核
    WEIGHT_ADJUSTED = "weight_adjusted"  # 权重调整
    PENDING_DATA = "pending_data"  # 等待更多数据

@dataclass
class DataRecord:
    """数据记录结构"""
    user_id: str
    text: str
    emotion_score: float
    quality_score: float
    timestamp: datetime
    word_count: int
    metadata: Optional[Dict] = None
    
    # 处理过程中添加的字段
    quality_level: Optional[DataQualityLevel] = None
    anomaly_level: Optional[AnomalyLevel] = None
    processing_status: Optional[ProcessingStatus] = None
    weight_factor: float = 1.0
    dsi_score: Optional[float] = None

@dataclass
class UserProfile:
    """用户画像信息"""
    user_id: str
    data_count: int
    time_span_days: int
    avg_emotion: float
    emotion_std: float
    quality_avg: float
    last_update: datetime
    confidence_level: float = 0.0
    is_cold_start: bool = True

class EdgeCaseHandler:
    """边缘情况处理器"""
    
    def __init__(self):
        self.cold_start_threshold = 20  # 新用户数据量阈值
        self.cold_start_days = 14      # 新用户时间跨度阈值
        self.sparse_frequency = 2      # 稀疏表达阈值（每周次数）
        self.emotion_range_threshold = 1.0  # 情绪表达单一化阈值
        
    def handle_cold_start(self, user_profile: UserProfile) -> Dict[str, Any]:
        """处理新用户冷启动"""
        adjustments = {
            'dsi_threshold': 0.3,  # 降低DSI阈值
            'baseline_emotion': 5.0,  # 中性基线
            'data_collection_priority': True,
            'min_quality_level': DataQualityLevel.B,
            'reeval_frequency': 10  # 每10条数据重新评估
        }
        
        logger.info(f"用户 {user_profile.user_id} 触发冷启动策略")
        return adjustments
    
    def handle_sparse_expression(self, user_profile: UserProfile) -> Dict[str, Any]:
        """处理数据稀疏或不规律表达"""
        adjustments = {
            'collection_period_weeks': 10,  # 延长收集周期
            'time_coverage_requirement': 0.4,  # 降低时段覆盖要求
            'extreme_value_weight': 1.5,  # 提高极值权重
            'anomaly_threshold': 3.0  # 宽松异常检测
        }
        
        logger.info(f"用户 {user_profile.user_id} 触发稀疏表达策略")
        return adjustments
    
    def handle_emotion_monotony(self, user_profile: UserProfile) -> Dict[str, Any]:
        """处理情绪表达单一化"""
        adjustments = {
            'use_relative_deviation': True,  # 使用相对偏离度
            'word_time_weight_boost': 1.3,  # 增加字数时间权重
            'observation_period_extend': True,  # 延长观察期
            'micro_pattern_detection': True  # 微小波动识别
        }
        
        logger.info(f"用户 {user_profile.user_id} 触发情绪单一化策略")
        return adjustments
    
    def handle_low_quality_persistent(self, user_profile: UserProfile) -> Dict[str, Any]:
        """处理数据质量持续低下"""
        adjustments = {
            'reevaluate_collection': True,  # 重新评估收集方式
            'personalized_quality_weights': True,  # 个性化质量权重
            'manual_annotation_required': True,  # 需要人工标注
            'special_user_flag': True  # 特殊用户标记
        }
        
        logger.info(f"用户 {user_profile.user_id} 触发低质量持续策略")
        return adjustments

class DataSelectionIntegrator:
    """数据选取流程集成器"""
    
    def __init__(self, db_path: str = "data_selection_integration.db"):
        self.db_path = db_path
        self.edge_case_handler = EdgeCaseHandler()
        self.user_profiles = {}  # 用户画像缓存
        self.processing_stats = defaultdict(int)  # 处理统计
        self.isolated_data = []  # 隔离数据
        self.lock = threading.Lock()
        
        # 初始化数据库
        self._init_database()
        
        # 配置参数
        self.dqs_threshold = 4.0  # DQS阈值
        self.dsi_threshold = 0.4  # DSI阈值
        self.weight_factors = {
            AnomalyLevel.NORMAL: 1.0,
            AnomalyLevel.LIGHT: 0.7,
            AnomalyLevel.MODERATE: 0.3,
            AnomalyLevel.SEVERE: 0.0
        }
        
    def _init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 数据记录表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS data_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    text TEXT NOT NULL,
                    emotion_score REAL NOT NULL,
                    quality_score REAL NOT NULL,
                    timestamp TEXT NOT NULL,
                    word_count INTEGER NOT NULL,
                    metadata TEXT,
                    quality_level TEXT,
                    anomaly_level TEXT,
                    processing_status TEXT,
                    weight_factor REAL DEFAULT 1.0,
                    dsi_score REAL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 用户画像表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_profiles (
                    user_id TEXT PRIMARY KEY,
                    data_count INTEGER DEFAULT 0,
                    time_span_days INTEGER DEFAULT 0,
                    avg_emotion REAL DEFAULT 5.0,
                    emotion_std REAL DEFAULT 0.0,
                    quality_avg REAL DEFAULT 0.0,
                    last_update TEXT,
                    confidence_level REAL DEFAULT 0.0,
                    is_cold_start BOOLEAN DEFAULT 1,
                    edge_case_flags TEXT DEFAULT '{}'
                )
            """)
            
            # 隔离数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS isolated_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    original_data TEXT NOT NULL,
                    isolation_reason TEXT NOT NULL,
                    isolation_time TEXT DEFAULT CURRENT_TIMESTAMP,
                    review_status TEXT DEFAULT 'pending',
                    reviewer_notes TEXT
                )
            """)
            
            # 处理统计表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS processing_stats (
                    date TEXT PRIMARY KEY,
                    total_processed INTEGER DEFAULT 0,
                    accepted INTEGER DEFAULT 0,
                    rejected INTEGER DEFAULT 0,
                    isolated INTEGER DEFAULT 0,
                    weight_adjusted INTEGER DEFAULT 0,
                    avg_processing_time REAL DEFAULT 0.0
                )
            """)
            
            conn.commit()
    
    def process_data_pipeline(self, data_record: DataRecord) -> Dict[str, Any]:
        """完整数据选取流程处理管道"""
        start_time = time.time()
        
        try:
            with self.lock:
                # 1. 数据质量验证
                quality_result = self._validate_data_quality(data_record)
                if not quality_result['passed']:
                    return self._handle_rejection(data_record, "质量不达标")
                
                # 2. 异常检测处理
                anomaly_result = self._detect_anomalies(data_record)
                data_record.anomaly_level = anomaly_result['level']
                
                # 3. 异常程度判断和处理
                if anomaly_result['level'] == AnomalyLevel.SEVERE:
                    return self._handle_isolation(data_record, anomaly_result['reason'])
                elif anomaly_result['level'] in [AnomalyLevel.MODERATE, AnomalyLevel.LIGHT]:
                    data_record.weight_factor = self.weight_factors[anomaly_result['level']]
                    data_record.processing_status = ProcessingStatus.WEIGHT_ADJUSTED
                
                # 4. 数据充分性评估
                sufficiency_result = self._evaluate_data_sufficiency(data_record)
                data_record.dsi_score = sufficiency_result['dsi_score']
                
                if sufficiency_result['dsi_score'] < self.dsi_threshold:
                    return self._handle_insufficient_data(data_record)
                
                # 5. 分层存储分配
                storage_result = self._allocate_layered_storage(data_record)
                
                # 6. 特征重要性评估
                importance_result = self._evaluate_feature_importance(data_record)
                
                # 7. 最终数据选取
                final_result = self._finalize_data_selection(data_record, storage_result, importance_result)
                
                # 8. 更新用户画像
                self._update_user_profile(data_record)
                
                # 9. 边缘情况检查
                edge_case_result = self._check_edge_cases(data_record.user_id)
                
                # 10. 记录处理统计
                processing_time = time.time() - start_time
                self._update_processing_stats(data_record.processing_status, processing_time)
                
                return {
                    'status': 'success',
                    'processing_status': data_record.processing_status.value,
                    'quality_level': data_record.quality_level.value,
                    'anomaly_level': data_record.anomaly_level.value,
                    'weight_factor': data_record.weight_factor,
                    'dsi_score': data_record.dsi_score,
                    'storage_layer': storage_result.get('target_layer'),
                    'importance_score': importance_result.get('score'),
                    'edge_cases': edge_case_result,
                    'processing_time': processing_time
                }
                
        except Exception as e:
            logger.error(f"数据处理管道错误: {str(e)}")
            return {
                'status': 'error',
                'error_message': str(e),
                'processing_time': time.time() - start_time
            }
    
    def _validate_data_quality(self, data_record: DataRecord) -> Dict[str, Any]:
        """数据质量验证"""
        dqs = data_record.quality_score
        
        # 确定质量等级
        if dqs >= 7.0:
            data_record.quality_level = DataQualityLevel.A
        elif dqs >= 5.5:
            data_record.quality_level = DataQualityLevel.B
        elif dqs >= 4.0:
            data_record.quality_level = DataQualityLevel.C
        else:
            data_record.quality_level = DataQualityLevel.D
        
        passed = dqs >= self.dqs_threshold
        
        return {
            'passed': passed,
            'dqs_score': dqs,
            'quality_level': data_record.quality_level,
            'reason': '质量分数不达标' if not passed else '质量验证通过'
        }
    
    def _detect_anomalies(self, data_record: DataRecord) -> Dict[str, Any]:
        """异常检测处理"""
        user_id = data_record.user_id
        
        # 获取用户历史数据进行异常检测
        historical_data = self._get_user_historical_data(user_id)
        
        if len(historical_data) < 5:  # 数据不足，使用默认检测
            return self._default_anomaly_detection(data_record)
        
        # 计算Z-score
        emotions = [d['emotion_score'] for d in historical_data]
        mean_emotion = np.mean(emotions)
        std_emotion = np.std(emotions)
        
        if std_emotion == 0:
            z_score = 0
        else:
            z_score = abs(data_record.emotion_score - mean_emotion) / std_emotion
        
        # 异常程度判断
        if z_score > 3.0:
            level = AnomalyLevel.SEVERE
            reason = f"情绪分数严重偏离(Z-score: {z_score:.2f})"
        elif z_score > 2.5:
            level = AnomalyLevel.MODERATE
            reason = f"情绪分数中度偏离(Z-score: {z_score:.2f})"
        elif z_score > 1.5:
            level = AnomalyLevel.LIGHT
            reason = f"情绪分数轻微偏离(Z-score: {z_score:.2f})"
        else:
            level = AnomalyLevel.NORMAL
            reason = "情绪分数正常"
        
        return {
            'level': level,
            'z_score': z_score,
            'reason': reason
        }
    
    def _default_anomaly_detection(self, data_record: DataRecord) -> Dict[str, Any]:
        """默认异常检测（数据不足时使用）"""
        emotion = data_record.emotion_score
        
        # 基于绝对值的简单异常检测
        if emotion < 1.0 or emotion > 9.0:
            level = AnomalyLevel.SEVERE
            reason = "情绪分数超出正常范围"
        elif emotion < 2.0 or emotion > 8.0:
            level = AnomalyLevel.MODERATE
            reason = "情绪分数接近极值"
        elif data_record.word_count < 5:
            level = AnomalyLevel.LIGHT
            reason = "文本长度过短"
        else:
            level = AnomalyLevel.NORMAL
            reason = "数据正常"
        
        return {
            'level': level,
            'z_score': 0.0,
            'reason': reason
        }
    
    def _evaluate_data_sufficiency(self, data_record: DataRecord) -> Dict[str, Any]:
        """数据充分性评估"""
        user_id = data_record.user_id
        user_data = self._get_user_data_summary(user_id)
        
        # 计算DSI分数
        data_volume_score = min(user_data['count'] / 50, 1.0)  # 50条为满分
        time_span_score = min(user_data['time_span_days'] / 30, 1.0)  # 30天为满分
        time_coverage_score = user_data['time_coverage']  # 时段覆盖度
        quality_score = user_data['avg_quality'] / 10.0  # 平均质量分数
        
        dsi_score = (
            data_volume_score * 0.4 +
            time_span_score * 0.3 +
            time_coverage_score * 0.15 +
            quality_score * 0.15
        )
        
        return {
            'dsi_score': dsi_score,
            'data_volume_score': data_volume_score,
            'time_span_score': time_span_score,
            'time_coverage_score': time_coverage_score,
            'quality_score': quality_score,
            'sufficient': dsi_score >= self.dsi_threshold
        }
    
    def _allocate_layered_storage(self, data_record: DataRecord) -> Dict[str, Any]:
        """分层存储分配"""
        now = datetime.now()
        data_time = data_record.timestamp
        days_old = (now - data_time).days
        
        # 根据时间确定存储层
        if days_old <= 7:
            target_layer = "working_memory"
            capacity = 50
        elif days_old <= 28:
            target_layer = "short_term_memory"
            capacity = 150
        elif days_old <= 112:
            target_layer = "long_term_memory"
            capacity = 300
        else:
            target_layer = "core_memory"
            capacity = 100
        
        return {
            'target_layer': target_layer,
            'capacity': capacity,
            'days_old': days_old
        }
    
    def _evaluate_feature_importance(self, data_record: DataRecord) -> Dict[str, Any]:
        """特征重要性评估"""
        # 基础重要性分数
        quality_weight = data_record.quality_score / 10.0
        emotion_weight = abs(data_record.emotion_score - 5.0) / 5.0  # 偏离中性的程度
        length_weight = min(data_record.word_count / 100, 1.0)  # 文本长度权重
        
        # 综合重要性分数
        importance_score = (
            quality_weight * 0.4 +
            emotion_weight * 0.3 +
            length_weight * 0.2 +
            data_record.weight_factor * 0.1
        )
        
        return {
            'score': importance_score,
            'quality_weight': quality_weight,
            'emotion_weight': emotion_weight,
            'length_weight': length_weight
        }
    
    def _finalize_data_selection(self, data_record: DataRecord, storage_result: Dict, importance_result: Dict) -> Dict[str, Any]:
        """最终数据选取"""
        # 存储到数据库
        self._store_data_record(data_record)
        
        # 设置最终状态
        if data_record.processing_status is None:
            data_record.processing_status = ProcessingStatus.ACCEPTED
        
        return {
            'selected': True,
            'storage_layer': storage_result['target_layer'],
            'importance_score': importance_result['score'],
            'final_weight': data_record.weight_factor
        }
    
    def _handle_rejection(self, data_record: DataRecord, reason: str) -> Dict[str, Any]:
        """处理数据拒绝"""
        data_record.processing_status = ProcessingStatus.REJECTED
        
        self.processing_stats['rejected'] += 1
        logger.info(f"数据被拒绝: {reason} - 用户: {data_record.user_id}")
        
        return {
            'status': 'rejected',
            'reason': reason,
            'processing_status': ProcessingStatus.REJECTED.value,
            'processing_time': 0.0
        }
    
    def _handle_isolation(self, data_record: DataRecord, reason: str) -> Dict[str, Any]:
        """处理数据隔离"""
        data_record.processing_status = ProcessingStatus.ISOLATED
        
        # 存储到隔离表
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO isolated_data (user_id, original_data, isolation_reason)
                VALUES (?, ?, ?)
            """, (data_record.user_id, json.dumps(asdict(data_record), default=str), reason))
            conn.commit()
        
        self.processing_stats['isolated'] += 1
        logger.warning(f"数据被隔离: {reason} - 用户: {data_record.user_id}")
        
        return {
            'status': 'isolated',
            'reason': reason,
            'processing_status': ProcessingStatus.ISOLATED.value,
            'processing_time': 0.0
        }
    
    def _handle_insufficient_data(self, data_record: DataRecord) -> Dict[str, Any]:
        """处理数据不足"""
        data_record.processing_status = ProcessingStatus.PENDING_DATA
        
        # 临时存储，等待更多数据
        self._store_data_record(data_record)
        
        logger.info(f"数据不足，等待更多数据 - 用户: {data_record.user_id}, DSI: {data_record.dsi_score}")
        
        return {
            'status': 'pending',
            'reason': '数据充分性不足',
            'dsi_score': data_record.dsi_score,
            'processing_status': ProcessingStatus.PENDING_DATA.value,
            'processing_time': 0.0
        }
    
    def _update_user_profile(self, data_record: DataRecord):
        """更新用户画像"""
        user_id = data_record.user_id
        
        # 获取或创建用户画像
        if user_id not in self.user_profiles:
            self.user_profiles[user_id] = UserProfile(
                user_id=user_id,
                data_count=0,
                time_span_days=0,
                avg_emotion=5.0,
                emotion_std=0.0,
                quality_avg=0.0,
                last_update=datetime.now()
            )
        
        profile = self.user_profiles[user_id]
        
        # 更新统计信息
        user_data = self._get_user_data_summary(user_id)
        profile.data_count = user_data['count']
        profile.time_span_days = user_data['time_span_days']
        profile.avg_emotion = user_data['avg_emotion']
        profile.emotion_std = user_data['emotion_std']
        profile.quality_avg = user_data['avg_quality']
        profile.last_update = datetime.now()
        
        # 判断是否还是冷启动状态
        profile.is_cold_start = (profile.data_count < 20 and profile.time_span_days < 14)
        
        # 计算置信度
        profile.confidence_level = min(profile.data_count / 50, 1.0) * min(profile.time_span_days / 30, 1.0)
        
        # 保存到数据库
        self._save_user_profile(profile)
    
    def _check_edge_cases(self, user_id: str) -> Dict[str, Any]:
        """检查边缘情况"""
        if user_id not in self.user_profiles:
            return {}
        
        profile = self.user_profiles[user_id]
        edge_cases = {}
        
        # 1. 冷启动检查
        if profile.is_cold_start:
            edge_cases['cold_start'] = self.edge_case_handler.handle_cold_start(profile)
        
        # 2. 稀疏表达检查
        weekly_frequency = profile.data_count / max(profile.time_span_days / 7, 1)
        if weekly_frequency < self.edge_case_handler.sparse_frequency:
            edge_cases['sparse_expression'] = self.edge_case_handler.handle_sparse_expression(profile)
        
        # 3. 情绪单一化检查
        if profile.emotion_std < self.edge_case_handler.emotion_range_threshold:
            edge_cases['emotion_monotony'] = self.edge_case_handler.handle_emotion_monotony(profile)
        
        # 4. 低质量持续检查
        if profile.quality_avg < 4.0 and profile.data_count > 10:
            edge_cases['low_quality_persistent'] = self.edge_case_handler.handle_low_quality_persistent(profile)
        
        return edge_cases
    
    def _get_user_historical_data(self, user_id: str) -> List[Dict]:
        """获取用户历史数据"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT emotion_score, quality_score, timestamp, word_count
                FROM data_records
                WHERE user_id = ? AND processing_status != 'rejected'
                ORDER BY timestamp DESC
                LIMIT 100
            """, (user_id,))
            
            results = cursor.fetchall()
            return [{
                'emotion_score': row[0],
                'quality_score': row[1],
                'timestamp': row[2],
                'word_count': row[3]
            } for row in results]
    
    def _get_user_data_summary(self, user_id: str) -> Dict[str, Any]:
        """获取用户数据摘要"""
        historical_data = self._get_user_historical_data(user_id)
        
        if not historical_data:
            return {
                'count': 0,
                'time_span_days': 0,
                'avg_emotion': 5.0,
                'emotion_std': 0.0,
                'avg_quality': 0.0,
                'time_coverage': 0.0
            }
        
        emotions = [d['emotion_score'] for d in historical_data]
        qualities = [d['quality_score'] for d in historical_data]
        timestamps = [datetime.fromisoformat(d['timestamp']) for d in historical_data]
        
        # 计算时间跨度
        if len(timestamps) > 1:
            time_span = (max(timestamps) - min(timestamps)).days
        else:
            time_span = 0
        
        # 计算时段覆盖度（简化版本）
        hours = [t.hour for t in timestamps]
        unique_hours = len(set(hours))
        time_coverage = unique_hours / 24.0
        
        return {
            'count': len(historical_data),
            'time_span_days': time_span,
            'avg_emotion': np.mean(emotions),
            'emotion_std': np.std(emotions),
            'avg_quality': np.mean(qualities),
            'time_coverage': time_coverage
        }
    
    def _store_data_record(self, data_record: DataRecord):
        """存储数据记录"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO data_records (
                    user_id, text, emotion_score, quality_score, timestamp,
                    word_count, metadata, quality_level, anomaly_level,
                    processing_status, weight_factor, dsi_score
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data_record.user_id,
                data_record.text,
                data_record.emotion_score,
                data_record.quality_score,
                data_record.timestamp.isoformat(),
                data_record.word_count,
                json.dumps(data_record.metadata) if data_record.metadata else None,
                data_record.quality_level.value if data_record.quality_level else None,
                data_record.anomaly_level.value if data_record.anomaly_level else None,
                data_record.processing_status.value if data_record.processing_status else None,
                data_record.weight_factor,
                data_record.dsi_score
            ))
            conn.commit()
    
    def _save_user_profile(self, profile: UserProfile):
        """保存用户画像"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO user_profiles (
                    user_id, data_count, time_span_days, avg_emotion,
                    emotion_std, quality_avg, last_update, confidence_level, is_cold_start
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                profile.user_id,
                profile.data_count,
                profile.time_span_days,
                profile.avg_emotion,
                profile.emotion_std,
                profile.quality_avg,
                profile.last_update.isoformat(),
                profile.confidence_level,
                profile.is_cold_start
            ))
            conn.commit()
    
    def _update_processing_stats(self, status: ProcessingStatus, processing_time: float):
        """更新处理统计"""
        today = datetime.now().date().isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 获取今日统计
            cursor.execute("SELECT * FROM processing_stats WHERE date = ?", (today,))
            result = cursor.fetchone()
            
            if result:
                # 更新现有记录
                total_processed = result[1] + 1
                accepted = result[2] + (1 if status == ProcessingStatus.ACCEPTED else 0)
                rejected = result[3] + (1 if status == ProcessingStatus.REJECTED else 0)
                isolated = result[4] + (1 if status == ProcessingStatus.ISOLATED else 0)
                weight_adjusted = result[5] + (1 if status == ProcessingStatus.WEIGHT_ADJUSTED else 0)
                avg_time = (result[6] * result[1] + processing_time) / total_processed
                
                cursor.execute("""
                    UPDATE processing_stats
                    SET total_processed = ?, accepted = ?, rejected = ?,
                        isolated = ?, weight_adjusted = ?, avg_processing_time = ?
                    WHERE date = ?
                """, (total_processed, accepted, rejected, isolated, weight_adjusted, avg_time, today))
            else:
                # 创建新记录
                accepted = 1 if status == ProcessingStatus.ACCEPTED else 0
                rejected = 1 if status == ProcessingStatus.REJECTED else 0
                isolated = 1 if status == ProcessingStatus.ISOLATED else 0
                weight_adjusted = 1 if status == ProcessingStatus.WEIGHT_ADJUSTED else 0
                
                cursor.execute("""
                    INSERT INTO processing_stats (
                        date, total_processed, accepted, rejected,
                        isolated, weight_adjusted, avg_processing_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (today, 1, accepted, rejected, isolated, weight_adjusted, processing_time))
            
            conn.commit()
    
    def get_processing_statistics(self, days: int = 7) -> Dict[str, Any]:
        """获取处理统计信息"""
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT SUM(total_processed), SUM(accepted), SUM(rejected),
                       SUM(isolated), SUM(weight_adjusted), AVG(avg_processing_time)
                FROM processing_stats
                WHERE date BETWEEN ? AND ?
            """, (start_date.isoformat(), end_date.isoformat()))
            
            result = cursor.fetchone()
            
            if result and result[0]:
                total = result[0]
                return {
                    'period_days': days,
                    'total_processed': total,
                    'accepted': result[1],
                    'rejected': result[2],
                    'isolated': result[3],
                    'weight_adjusted': result[4],
                    'avg_processing_time': result[5],
                    'acceptance_rate': result[1] / total if total > 0 else 0,
                    'rejection_rate': result[2] / total if total > 0 else 0,
                    'isolation_rate': result[3] / total if total > 0 else 0
                }
            else:
                return {
                    'period_days': days,
                    'total_processed': 0,
                    'accepted': 0,
                    'rejected': 0,
                    'isolated': 0,
                    'weight_adjusted': 0,
                    'avg_processing_time': 0,
                    'acceptance_rate': 0,
                    'rejection_rate': 0,
                    'isolation_rate': 0
                }
    
    def get_isolated_data_for_review(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取待审核的隔离数据"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, user_id, original_data, isolation_reason, isolation_time
                FROM isolated_data
                WHERE review_status = 'pending'
                ORDER BY isolation_time DESC
                LIMIT ?
            """, (limit,))
            
            results = cursor.fetchall()
            return [{
                'id': row[0],
                'user_id': row[1],
                'original_data': json.loads(row[2]),
                'isolation_reason': row[3],
                'isolation_time': row[4]
            } for row in results]
    
    def review_isolated_data(self, isolation_id: int, decision: str, notes: str = "") -> bool:
        """审核隔离数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE isolated_data
                    SET review_status = ?, reviewer_notes = ?
                    WHERE id = ?
                """, (decision, notes, isolation_id))
                conn.commit()
                
                if decision == 'approved':
                    # 如果批准，重新处理数据
                    cursor.execute("SELECT original_data FROM isolated_data WHERE id = ?", (isolation_id,))
                    result = cursor.fetchone()
                    if result:
                        original_data = json.loads(result[0])
                        # 重新创建DataRecord并处理
                        data_record = DataRecord(**original_data)
                        self.process_data_pipeline(data_record)
                
                return True
        except Exception as e:
            logger.error(f"审核隔离数据失败: {str(e)}")
            return False
    
    def get_user_edge_case_status(self, user_id: str) -> Dict[str, Any]:
        """获取用户边缘情况状态"""
        if user_id not in self.user_profiles:
            return {'status': 'no_profile'}
        
        profile = self.user_profiles[user_id]
        edge_cases = self._check_edge_cases(user_id)
        
        return {
            'user_id': user_id,
            'profile': asdict(profile),
            'edge_cases': edge_cases,
            'recommendations': self._generate_recommendations(profile, edge_cases)
        }
    
    def _generate_recommendations(self, profile: UserProfile, edge_cases: Dict) -> List[str]:
        """生成处理建议"""
        recommendations = []
        
        if 'cold_start' in edge_cases:
            recommendations.append("建议增加数据收集频率，优先保留B级以上质量数据")
        
        if 'sparse_expression' in edge_cases:
            recommendations.append("建议延长数据收集周期，关注情绪极值数据")
        
        if 'emotion_monotony' in edge_cases:
            recommendations.append("建议使用相对偏离度评估，增加字数和时间维度权重")
        
        if 'low_quality_persistent' in edge_cases:
            recommendations.append("建议重新评估数据收集方式，考虑个性化质量权重")
        
        if not edge_cases:
            recommendations.append("用户数据处理正常，继续当前策略")
        
        return recommendations

# 测试和使用示例
if __name__ == "__main__":
    # 创建数据选取集成器
    integrator = DataSelectionIntegrator()
    
    # 测试数据
    test_records = [
        DataRecord(
            user_id="user_001",
            text="今天心情不错，工作很顺利",
            emotion_score=7.5,
            quality_score=8.2,
            timestamp=datetime.now(),
            word_count=12
        ),
        DataRecord(
            user_id="user_001",
            text="感觉很糟糕",
            emotion_score=2.1,
            quality_score=3.5,  # 低质量
            timestamp=datetime.now(),
            word_count=5
        ),
        DataRecord(
            user_id="user_002",
            text="今天是美好的一天，阳光明媚，心情愉悦",
            emotion_score=8.8,
            quality_score=9.1,
            timestamp=datetime.now(),
            word_count=16
        ),
        DataRecord(
            user_id="user_001",
            text="极度愤怒！！！",
            emotion_score=1.0,  # 极值，可能触发异常检测
            quality_score=6.0,
            timestamp=datetime.now(),
            word_count=6
        )
    ]
    
    print("=== 1.1.6 数据选取流程集成与边缘情况处理测试 ===")
    print()
    
    # 处理测试数据
    for i, record in enumerate(test_records, 1):
        print(f"处理第 {i} 条数据:")
        print(f"用户: {record.user_id}")
        print(f"文本: {record.text}")
        print(f"情绪分数: {record.emotion_score}")
        print(f"质量分数: {record.quality_score}")
        
        result = integrator.process_data_pipeline(record)
        
        print(f"处理结果: {result['status']}")
        if result['status'] == 'success':
            print(f"  - 处理状态: {result['processing_status']}")
            print(f"  - 质量等级: {result['quality_level']}")
            print(f"  - 异常等级: {result['anomaly_level']}")
            print(f"  - 权重因子: {result['weight_factor']}")
            print(f"  - DSI分数: {result['dsi_score']:.3f}")
            print(f"  - 存储层: {result['storage_layer']}")
            print(f"  - 重要性分数: {result['importance_score']:.3f}")
            if result['edge_cases']:
                print(f"  - 边缘情况: {list(result['edge_cases'].keys())}")
        else:
            print(f"  - 错误信息: {result.get('error_message', 'Unknown error')}")
        
        print(f"  - 处理时间: {result['processing_time']:.4f}秒")
        print("-" * 50)
    
    # 获取处理统计
    print("\n=== 处理统计信息 ===")
    stats = integrator.get_processing_statistics()
    print(f"总处理数量: {stats['total_processed']}")
    print(f"接受率: {stats['acceptance_rate']:.2%}")
    print(f"拒绝率: {stats['rejection_rate']:.2%}")
    print(f"隔离率: {stats['isolation_rate']:.2%}")
    print(f"平均处理时间: {stats['avg_processing_time']:.4f}秒")
    
    # 检查边缘情况
    print("\n=== 用户边缘情况状态 ===")
    for user_id in ["user_001", "user_002"]:
        edge_status = integrator.get_user_edge_case_status(user_id)
        if edge_status['status'] != 'no_profile':
            print(f"\n用户 {user_id}:")
            profile = edge_status['profile']
            print(f"  - 数据量: {profile['data_count']}条")
            print(f"  - 时间跨度: {profile['time_span_days']}天")
            print(f"  - 平均情绪: {profile['avg_emotion']:.2f}")
            print(f"  - 情绪标准差: {profile['emotion_std']:.2f}")
            print(f"  - 置信度: {profile['confidence_level']:.2f}")
            print(f"  - 冷启动状态: {profile['is_cold_start']}")
            
            if edge_status['edge_cases']:
                print(f"  - 边缘情况: {list(edge_status['edge_cases'].keys())}")
            
            print(f"  - 处理建议:")
            for rec in edge_status['recommendations']:
                print(f"    * {rec}")
    
    # 获取隔离数据
    print("\n=== 隔离数据审核 ===")
    isolated_data = integrator.get_isolated_data_for_review()
    if isolated_data:
        print(f"发现 {len(isolated_data)} 条隔离数据待审核:")
        for data in isolated_data:
            print(f"  - ID: {data['id']}, 用户: {data['user_id']}")
            print(f"    隔离原因: {data['isolation_reason']}")
            print(f"    隔离时间: {data['isolation_time']}")
    else:
        print("暂无隔离数据需要审核")
    
    print("\n=== 测试完成 ===")