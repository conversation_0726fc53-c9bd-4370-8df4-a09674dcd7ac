#!/usr/bin/env python3
"""
调试版本：深入分析计算9策略选择逻辑
"""

import json
import asyncio
from calculation_9_strategy_matching import main, <PERSON>rg<PERSON>, StrategyMatcher

def debug_strategy_selection():
    """详细调试策略选择过程"""
    
    # 用户提供的输入数据
    test_input = {
        "P25": "5.6",
        "P50": "7.6", 
        "P75": "7.8",
        "adaptability_score": "0.83",
        "cem_confidence": "0.553",
        "cem_grade": "基本稳定",
        "cem_value": "-0.226",
        "confidence_level": "0.804",
        "coordination_index": "0.305",
        "crisis_probability": "0.417",
        "ei_confidence": "0.858",
        "ei_value": "0.733",
        "eii_value": "0.46",
        "final_P25": "5.20",
        "final_P50": "8.53",
        "final_P75": "8.60",
        "final_conf_score": "0.13",
        "health_score": "0.69",
        "opportunity_windows": [],
        "risk_level": "中风险",
        "rsi_value": "0.476",
        "stability_trend": "稳定下降",
        "trend_prediction": {
            "medium_term_trend": {
                "confidence": 0.807,
                "direction": "保持稳定",
                "magnitude": 0.08,
                "time_horizon": "3-12个月"
            },
            "short_term_trend": {
                "confidence": 0.949,
                "direction": "保持稳定", 
                "magnitude": 0.1,
                "time_horizon": "1-3个月"
            }
        },
        "user_type": "适应调整型"
    }
    
    print("=== 计算9策略选择调试分析 ===")
    
    # 创建策略匹配器实例
    matcher = StrategyMatcher()
    
    # 1. 参数处理分析
    print("\n1. 参数处理分析:")
    user_type = test_input.get('user_type', '适应调整型')
    crisis_prob = float(test_input.get('crisis_probability', 0.5))
    rsi_value = float(test_input.get('rsi_value', 0.5))
    
    print(f"   用户类型: {user_type}")
    print(f"   危机概率: {crisis_prob}")
    print(f"   RSI值: {rsi_value}")
    print(f"   RSI < 0.6 (需要关系维护): {rsi_value < 0.6}")
    
    # 2. 用户权重分析
    print("\n2. 用户权重分析:")
    user_weights = matcher.user_type_weights.get(user_type, {'emotional': 0.33, 'social': 0.33, 'growth': 0.34})
    print(f"   情绪权重: {user_weights['emotional']} (>= 0.3: {user_weights['emotional'] >= 0.3})")
    print(f"   社交权重: {user_weights['social']} (>= 0.3: {user_weights['social'] >= 0.3})")
    print(f"   成长权重: {user_weights['growth']} (>= 0.3: {user_weights['growth'] >= 0.3})")
    
    # 3. 策略库详细分析
    print("\n3. 策略库详细分析:")
    
    total_strategies = 0
    selected_count = 0
    
    # 危机干预策略分析
    print("\n   3.1 危机干预策略 (crisis_prob > 0.7 才触发):")
    print(f"       当前危机概率: {crisis_prob} > 0.7? {crisis_prob > 0.7}")
    if crisis_prob > 0.7:
        for strategy_id, strategy_info in matcher.strategy_library['crisis_intervention'].items():
            total_strategies += 1
            crisis_threshold = float(strategy_info.get('crisis_threshold', 1.0))
            suitable = user_type in strategy_info['suitable_types']
            threshold_ok = crisis_prob >= crisis_threshold
            
            print(f"       - {strategy_id}: suitable={suitable}, threshold_ok={threshold_ok} ({crisis_prob} >= {crisis_threshold})")
            if suitable and threshold_ok:
                selected_count += 1
    else:
        for strategy_id in matcher.strategy_library['crisis_intervention'].keys():
            total_strategies += 1
            print(f"       - {strategy_id}: 跳过 (危机概率不足)")
    
    # 情绪调节策略分析
    print(f"\n   3.2 情绪调节策略 (权重 >= 0.3: {user_weights['emotional'] >= 0.3}):")
    if user_weights['emotional'] >= 0.3:
        for strategy_id, strategy_info in matcher.strategy_library['emotional_regulation'].items():
            total_strategies += 1
            crisis_threshold = float(strategy_info.get('crisis_threshold', 1.0))
            suitable = user_type in strategy_info['suitable_types']
            threshold_ok = crisis_prob <= crisis_threshold
            
            print(f"       - {strategy_id}: suitable={suitable} {strategy_info['suitable_types']}, threshold_ok={threshold_ok} ({crisis_prob} <= {crisis_threshold})")
            if suitable and threshold_ok:
                selected_count += 1
                print(f"         ✅ 选中")
    else:
        for strategy_id in matcher.strategy_library['emotional_regulation'].keys():
            total_strategies += 1
            print(f"       - {strategy_id}: 跳过 (权重不足)")
    
    # 社交互动策略分析
    print(f"\n   3.3 社交互动策略 (权重 >= 0.3: {user_weights['social'] >= 0.3}):")
    if user_weights['social'] >= 0.3:
        for strategy_id, strategy_info in matcher.strategy_library['social_interaction'].items():
            total_strategies += 1
            crisis_threshold = float(strategy_info.get('crisis_threshold', 1.0))
            suitable = user_type in strategy_info['suitable_types']
            threshold_ok = crisis_prob <= crisis_threshold
            
            print(f"       - {strategy_id}: suitable={suitable} {strategy_info['suitable_types']}, threshold_ok={threshold_ok} ({crisis_prob} <= {crisis_threshold})")
            if suitable and threshold_ok:
                selected_count += 1
                print(f"         ✅ 选中")
    else:
        for strategy_id in matcher.strategy_library['social_interaction'].keys():
            total_strategies += 1
            print(f"       - {strategy_id}: 跳过 (权重不足)")
    
    # 个人成长策略分析
    print(f"\n   3.4 个人成长策略 (权重 >= 0.3: {user_weights['growth'] >= 0.3}):")
    if user_weights['growth'] >= 0.3:
        for strategy_id, strategy_info in matcher.strategy_library['personal_growth'].items():
            total_strategies += 1
            crisis_threshold = float(strategy_info.get('crisis_threshold', 1.0))
            suitable = user_type in strategy_info['suitable_types']
            threshold_ok = crisis_prob <= crisis_threshold
            
            print(f"       - {strategy_id}: suitable={suitable} {strategy_info['suitable_types']}, threshold_ok={threshold_ok} ({crisis_prob} <= {crisis_threshold})")
            if suitable and threshold_ok:
                selected_count += 1
                print(f"         ✅ 选中")
    else:
        for strategy_id in matcher.strategy_library['personal_growth'].keys():
            total_strategies += 1
            print(f"       - {strategy_id}: 跳过 (权重不足)")
    
    # 关系维护策略分析
    print(f"\n   3.5 关系维护策略 (RSI < 0.6: {rsi_value < 0.6}):")
    if rsi_value < 0.6:
        for strategy_id, strategy_info in matcher.strategy_library['relationship_maintenance'].items():
            total_strategies += 1
            crisis_threshold = float(strategy_info.get('crisis_threshold', 1.0))
            suitable = user_type in strategy_info['suitable_types']
            threshold_ok = crisis_prob <= crisis_threshold
            
            print(f"       - {strategy_id}: suitable={suitable} {strategy_info['suitable_types']}, threshold_ok={threshold_ok} ({crisis_prob} <= {crisis_threshold})")
            if suitable and threshold_ok:
                selected_count += 1
                print(f"         ✅ 选中")
    else:
        for strategy_id in matcher.strategy_library['relationship_maintenance'].keys():
            total_strategies += 1
            print(f"       - {strategy_id}: 跳过 (RSI值过高)")
    
    print(f"\n=== 总结 ===")
    print(f"策略库总数: {total_strategies}")
    print(f"预期选中数: {selected_count}")
    
    # 4. 实际执行对比
    print(f"\n4. 实际执行结果对比:")
    try:
        args = Args(test_input)
        result = asyncio.run(main(args))
        actual_count = len(result.get('selected_strategies', []))
        print(f"   实际选中数: {actual_count}")

        if actual_count != selected_count:
            print(f"   ⚠️  预期与实际不符！预期{selected_count}个，实际{actual_count}个")
            print(f"   💡 可能原因：代码限制了最多返回5个策略，或按适配度排序后截取")
        else:
            print(f"   ✅ 预期与实际一致")

        # 显示实际选中的策略及其适配度分数
        print(f"\n   实际选中的策略（按适配度排序）:")
        for i, strategy in enumerate(result.get('selected_strategies', []), 1):
            adaptability = strategy.get('adaptability_score', 0)
            print(f"   {i}. {strategy.get('strategy_name')} ({strategy.get('category')}.{strategy.get('strategy_id')}) - 适配度: {adaptability:.4f}")

        # 显示综合适配度
        comprehensive_adaptability = result.get('comprehensive_adaptability', 0)
        print(f"\n   综合适配度: {comprehensive_adaptability:.4f}")

    except Exception as e:
        print(f"   ❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_strategy_selection()
