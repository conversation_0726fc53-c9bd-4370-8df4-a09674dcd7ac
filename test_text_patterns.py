#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本模式检测功能测试脚本
基于1.1.2节数据质量验证体系的_detect_text_patterns函数测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 方案分析报告 import DataQualityValidator

def test_text_patterns():
    """测试文本模式检测功能"""
    validator = DataQualityValidator()
    
    print("=== 1.1.2节 文本模式检测功能测试 ===")
    print("测试_detect_text_patterns函数如何过滤无意义内容\n")
    
    # 测试用例：包含各种类型的文本
    test_cases = [
        # 正常文本（应该返回False）
        ("今天天气真好，心情很不错！", False, "正常表达"),
        ("工作压力有点大，需要调整一下", False, "正常表达"),
        ("这个产品功能很棒，用户体验不错", False, "正常表达"),
        
        # 重复字符（应该返回True）
        ("哈哈哈哈哈哈", True, "重复字符-哈"),
        ("啊啊啊啊啊啊啊", True, "重复字符-啊"),
        ("呵呵呵呵呵呵", True, "重复字符-呵"),
        ("嗯嗯嗯嗯嗯嗯", True, "重复字符-嗯"),
        
        # 测试文本（应该返回True）
        ("test123", True, "包含test"),
        ("这是测试内容", True, "包含测试"),
        ("aaaaaa", True, "包含aaa"),
        ("111222", True, "包含111"),
        ("xxxyyy", True, "包含xxx"),
        ("....", True, "包含...."),
        
        # 异常长度（应该返回True）
        ("a", True, "过短文本"),
        ("", True, "空文本"),
        ("a" * 501, True, "过长文本"),
        
        # 纯数字（应该返回True）
        ("12345678", True, "纯数字且较长"),
        ("999999", True, "纯数字且较长"),
        
        # 重复词汇（应该返回True）
        ("好好好好好好", True, "重复词汇-好"),
        ("不不不不不不", True, "重复词汇-不"),
        ("是是是是是是", True, "重复词汇-是"),
        
        # 边界情况
        ("哈哈", False, "短重复字符-正常"),
        ("123", False, "短数字-正常"),
        ("好好", False, "短重复词汇-正常"),
        (None, True, "None值"),
        (123, True, "非字符串类型"),
    ]
    
    print("测试结果：")
    print("-" * 80)
    print(f"{'文本内容':<25} {'预期结果':<10} {'实际结果':<10} {'测试状态':<10} {'说明'}")
    print("-" * 80)
    
    passed = 0
    failed = 0
    
    for text, expected, description in test_cases:
        try:
            result = validator._detect_text_patterns(text)
            status = "✓ 通过" if result == expected else "✗ 失败"
            
            # 处理显示文本
            display_text = str(text) if text is not None else "None"
            if len(display_text) > 20:
                display_text = display_text[:17] + "..."
            
            print(f"{display_text:<25} {expected!s:<10} {result!s:<10} {status:<10} {description}")
            
            if result == expected:
                passed += 1
            else:
                failed += 1
                
        except Exception as e:
            print(f"{str(text)[:20]:<25} {expected!s:<10} {'ERROR':<10} {'✗ 异常':<10} {str(e)}")
            failed += 1
    
    print("-" * 80)
    print(f"测试总结：通过 {passed} 个，失败 {failed} 个，总计 {passed + failed} 个")
    
    # 展示具体的"哈哈哈"过滤效果
    print("\n=== 专门测试'哈哈哈'类型文本的过滤效果 ===")
    haha_tests = [
        "哈哈哈",
        "哈哈哈哈", 
        "哈哈哈哈哈",
        "哈哈哈哈哈哈哈",
        "呵呵呵呵呵",
        "嘿嘿嘿嘿嘿",
        "啊啊啊啊啊啊"
    ]
    
    for text in haha_tests:
        result = validator._detect_text_patterns(text)
        status = "🚫 被过滤" if result else "✅ 通过"
        print(f"文本: '{text}' -> {status}")
    
    return passed, failed

def demo_integration_with_quality_system():
    """演示文本模式检测与质量验证系统的集成"""
    print("\n=== 文本模式检测与数据质量验证系统集成演示 ===")
    
    from 方案分析报告 import EmotionData, main, Args
    import asyncio
    
    # 创建包含可疑文本的测试数据
    test_data = [
        {
            "bstudio_create_time": "2024-01-15 14:30:00",
            "conversation": "今天天气真好，心情很不错！",  # 正常文本
            "emo_value": "8.0",
            "number": "1"
        },
        {
            "bstudio_create_time": "2024-01-15 15:30:00", 
            "conversation": "哈哈哈哈哈哈哈哈",  # 可疑文本：重复字符
            "emo_value": "7.0",
            "number": "2"
        },
        {
            "bstudio_create_time": "2024-01-15 16:30:00",
            "conversation": "这是测试内容aaa",  # 可疑文本：包含测试关键词
            "emo_value": "6.0", 
            "number": "3"
        },
        {
            "bstudio_create_time": "2024-01-15 17:30:00",
            "conversation": "工作有点累，但还能坚持",  # 正常文本
            "emo_value": "5.5",
            "number": "4"
        }
    ]
    
    async def run_demo():
        args = Args({'outputList': test_data})
        result = await main(args)
        
        print("\n质量验证结果：")
        for i, item in enumerate(result['validation_results']):
            print(f"\n--- 样本 {i+1} ---")
            if 'emotion_analysis' in item:
                emotion = item['emotion_analysis']
                print(f"文本内容: {emotion.get('expression_text', 'N/A')}")
            
            if 'quality_assessment' in item:
                assessment = item['quality_assessment']
                print(f"DQS总分: {assessment['dqs_score']}")
                print(f"质量等级: {assessment['quality_level']}")
                print(f"处理决策: {assessment['processing_decision']}")
            
            if 'issues' in item and item['issues']:
                print(f"质量问题: {', '.join(item['issues'])}")
            else:
                print("质量问题: 无")
    
    asyncio.run(run_demo())

if __name__ == "__main__":
    # 运行文本模式检测测试
    passed, failed = test_text_patterns()
    
    # 运行集成演示
    demo_integration_with_quality_system()
    
    print(f"\n=== 最终测试结果 ===")
    print(f"_detect_text_patterns函数测试：通过 {passed} 个，失败 {failed} 个")
    if failed == 0:
        print("🎉 所有测试通过！_detect_text_patterns函数已成功实现并集成到数据质量验证系统中。")
    else:
        print(f"⚠️  有 {failed} 个测试失败，需要进一步调试。")