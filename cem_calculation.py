#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CEM情绪动量计算实例
基于用户提供的数据进行实际计算
"""

from datetime import datetime
import json
from typing import List, Dict

# 用户提供的数据
data = {
    "outputList": [
        {"bstudio_create_time": "2025-06-14 23:38:10 +0800 CST", "emo_value": "5", "number": "3"},
        {"bstudio_create_time": "2025-06-14 23:37:29 +0800 CST", "emo_value": "9", "number": "3"},
        {"bstudio_create_time": "2025-06-14 23:33:30 +0800 CST", "emo_value": "9", "number": "3"},
        {"bstudio_create_time": "2025-06-14 23:28:25 +0800 CST", "emo_value": "9", "number": "2"},
        {"bstudio_create_time": "2025-06-14 23:27:12 +0800 CST", "emo_value": "7", "number": "3"},
        {"bstudio_create_time": "2025-06-14 23:25:08 +0800 CST", "emo_value": "9", "number": "3"},
        {"bstudio_create_time": "2025-06-14 23:08:40 +0800 CST", "emo_value": "5", "number": "3"},
        {"bstudio_create_time": "2025-06-14 19:23:12 +0800 CST", "emo_value": "9", "number": "2"},
        {"bstudio_create_time": "2025-06-14 19:20:11 +0800 CST", "emo_value": "10", "number": "3"},
        {"bstudio_create_time": "2025-06-14 19:12:04 +0800 CST", "emo_value": "5", "number": "4"},
        {"bstudio_create_time": "2025-06-14 19:08:38 +0800 CST", "emo_value": "9", "number": "2"},
        {"bstudio_create_time": "2025-06-14 19:06:05 +0800 CST", "emo_value": "5", "number": "3"},
        {"bstudio_create_time": "2025-06-14 18:28:07 +0800 CST", "emo_value": "6", "number": "2"},
        {"bstudio_create_time": "2025-06-14 18:19:15 +0800 CST", "emo_value": "5", "number": "3"},
        {"bstudio_create_time": "2025-06-14 18:15:04 +0800 CST", "emo_value": "7", "number": "3"}
    ]
}

def parse_timestamp(time_str: str) -> datetime:
    """解析时间戳"""
    # 移除时区信息，简化处理
    time_str = time_str.replace(" +0800 CST", "")
    return datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")

def calculate_baseline(scores: List[float]) -> Dict:
    """计算个人情绪基线（P25, P50, P75）"""
    sorted_scores = sorted(scores)
    n = len(sorted_scores)
    
    def percentile(data, p):
        index = (len(data) - 1) * p
        lower = int(index)
        upper = min(lower + 1, len(data) - 1)
        weight = index - lower
        return data[lower] * (1 - weight) + data[upper] * weight
    
    return {
        'P25': round(percentile(sorted_scores, 0.25), 2),
        'P50': round(percentile(sorted_scores, 0.50), 2),
        'P75': round(percentile(sorted_scores, 0.75), 2)
    }

def analyze_time_pattern(timestamps: List[datetime]) -> Dict:
    """分析时间模式"""
    intervals = [(timestamps[i] - timestamps[i-1]).total_seconds() / 60 
                for i in range(1, len(timestamps))]
    
    mean_interval = sum(intervals) / len(intervals)
    std_interval = (sum((x - mean_interval)**2 for x in intervals) / len(intervals))**0.5
    cv_time = std_interval / mean_interval if mean_interval > 0 else 0
    
    if cv_time < 0.3:
        time_type = "高度规律型"
        regularity_score = 0.9
    elif cv_time < 0.7:
        time_type = "中等规律型"
        regularity_score = 0.6
    else:
        time_type = "随意型"
        regularity_score = 0.3
    
    return {
        'mean_interval': round(mean_interval, 2),
        'std_interval': round(std_interval, 2),
        'cv_time': round(cv_time, 3),
        'time_type': time_type,
        'regularity_score': regularity_score,
        'intervals': [round(i, 2) for i in intervals]
    }

def calculate_personalized_sensitivity(time_pattern: Dict, user_emotional_type: str = '波动型') -> float:
    """计算个性化时间敏感系数"""
    base_sensitivity_map = {
        '乐观型': 1.2,
        '低落型': 2.0,
        '波动型': 1.8,
        '平稳型': 1.0
    }
    
    base_sensitivity = base_sensitivity_map.get(user_emotional_type, 1.5)
    cultural_adjustment = 0.85  # 中文用户调整
    pattern_adjustment = min(2.5, max(0.5, time_pattern['std_interval'] * 0.01))
    regularity_adjustment = 1.0 + (1.0 - time_pattern['regularity_score']) * 0.5
    
    final_sensitivity = (base_sensitivity * cultural_adjustment * 
                        pattern_adjustment * regularity_adjustment)
    
    return max(0.5, min(3.0, final_sensitivity))

def calculate_dynamic_weights(timestamps: List[datetime], time_sensitivity: float) -> List[float]:
    """计算动态时间权重"""
    if len(timestamps) < 2:
        return []
    
    latest_time = timestamps[-1]
    weights = []
    
    for i in range(1, len(timestamps)):
        change_time = timestamps[i]
        time_gap = (latest_time - change_time).total_seconds() / 60
        
        weight = 1.0 / (1.0 + time_gap / time_sensitivity)
        
        if time_gap > 360:  # 超过6小时
            weight *= 0.7
        elif time_gap < 5:  # 极短间隔
            weight *= 1.2
        
        weight = max(0.1, min(1.0, weight))
        weights.append(weight)
    
    return weights

def calculate_cem_confidence(data_count: int, time_span: float, weight_distribution: List[float]) -> float:
    """计算CEM信心度"""
    data_factor = min(1.0, data_count / 10.0)
    
    if time_span < 1:
        time_factor = 0.6
    elif time_span <= 24:
        time_factor = 1.0
    elif time_span <= 168:
        time_factor = 0.9
    else:
        time_factor = 0.7
    
    if len(weight_distribution) > 1:
        weight_mean = sum(weight_distribution) / len(weight_distribution)
        weight_std = (sum((w - weight_mean)**2 for w in weight_distribution) / len(weight_distribution))**0.5
        cv = weight_std / weight_mean if weight_mean > 0 else 1.0
        
        if 0.3 <= cv <= 0.7:
            weight_factor = 1.0
        elif cv < 0.3:
            weight_factor = 0.7
        elif cv <= 1.0:
            weight_factor = 0.8
        else:
            weight_factor = 0.6
    else:
        weight_factor = 0.5
    
    confidence = (data_factor * 0.4 + time_factor * 0.4 + weight_factor * 0.2)
    return max(0.0, min(1.0, confidence))

def compute_cem_with_constraints(scores: List[float], timestamps: List[datetime], 
                               baseline: Dict, time_weights: List[float], 
                               user_emotional_type: str = '波动型') -> Dict:
    """CEM核心计算"""
    type_adjustment_map = {
        '乐观型': 0.85,
        '低落型': 1.25,
        '波动型': 1.15,
        '平稳型': 0.90
    }
    
    type_adjustment = type_adjustment_map.get(user_emotional_type, 1.0)
    
    # 相对位置计算
    p50 = baseline['P50']
    p25, p75 = baseline['P25'], baseline['P75']
    range_size = max(1.0, p75 - p25)
    
    relative_positions = [(score - p50) / range_size for score in scores]
    emotional_changes = [relative_positions[i] - relative_positions[i-1] 
                        for i in range(1, len(relative_positions))]
    
    if len(emotional_changes) == 0:
        return {'cem': 0.0, 'confidence': 0.0, 'weights_used': [], 'changes': [], 'type_adjustment': type_adjustment}
    
    # 加权CEM计算
    weighted_sum = sum(change * weight for change, weight in zip(emotional_changes, time_weights))
    weight_sum = sum(time_weights)
    
    cem_raw = weighted_sum / weight_sum if weight_sum > 0 else 0.0
    cem_adjusted = cem_raw * type_adjustment
    cem_final = max(-2.0, min(2.0, cem_adjusted))
    
    return {
        'cem': round(cem_final, 3),
        'confidence': 0.0,  # 将在主函数中计算
        'weights_used': [round(w, 3) for w in time_weights],
        'changes': [round(c, 3) for c in emotional_changes],
        'type_adjustment': round(type_adjustment, 3),
        'relative_positions': [round(p, 3) for p in relative_positions]
    }

def calculate_cem_enhanced(scores: List[float], timestamps: List[datetime], 
                          user_emotional_type: str = '波动型') -> Dict:
    """CEM情绪动量增强计算方法"""
    
    # 数据验证
    if len(scores) < 2 or len(timestamps) < 2:
        return {'error': '数据不足，至少需要2个数据点'}
    
    if len(scores) != len(timestamps):
        return {'error': '分数序列和时间戳序列长度不匹配'}
    
    # 1. 计算个人情绪基线
    baseline = calculate_baseline(scores)
    
    # 2. 时间模式分析
    time_pattern = analyze_time_pattern(timestamps)
    
    # 3. 个性化时间敏感系数
    time_sensitivity = calculate_personalized_sensitivity(time_pattern, user_emotional_type)
    
    # 4. 动态时间权重
    time_weights = calculate_dynamic_weights(timestamps, time_sensitivity)
    
    # 5. CEM核心计算
    cem_result = compute_cem_with_constraints(scores, timestamps, baseline, time_weights, user_emotional_type)
    
    # 6. 信心度计算
    time_span_hours = (timestamps[-1] - timestamps[0]).total_seconds() / 3600
    confidence = calculate_cem_confidence(len(scores), time_span_hours, time_weights)
    
    # 7. 趋势强度计算
    trend_strength = min(1.0, abs(cem_result['cem']) / 2.0)
    
    return {
        'cem': round(cem_result['cem'], 2),
        'confidence': round(confidence, 2),
        'trend_strength': round(trend_strength, 2),
        'time_sensitivity': round(time_sensitivity, 2),
        'baseline': baseline,
        'time_pattern': time_pattern,
        'calculation_details': {
            'weights_used': cem_result['weights_used'],
            'emotional_changes': cem_result['changes'],
            'relative_positions': cem_result['relative_positions'],
            'type_adjustment': cem_result['type_adjustment']
        }
    }

def main():
    """主函数：处理用户数据并计算CEM"""
    print("=" * 60)
    print("CEM情绪动量计算实例")
    print("=" * 60)
    
    # 解析数据
    scores = []
    timestamps = []
    
    # 注意：数据中有一个时间顺序错误，需要修正
    raw_data = data["outputList"]
    
    # 先解析所有数据
    parsed_data = []
    for item in raw_data:
        parsed_data.append({
            'timestamp': parse_timestamp(item['bstudio_create_time']),
            'score': float(item['emo_value']),
            'number': int(item['number'])
        })
    
    # 按时间排序
    parsed_data.sort(key=lambda x: x['timestamp'])
    
    # 提取排序后的数据
    for item in parsed_data:
        timestamps.append(item['timestamp'])
        scores.append(item['score'])
    
    print(f"原始数据点数量: {len(scores)}")
    print(f"时间范围: {timestamps[0]} 到 {timestamps[-1]}")
    print(f"情绪分数范围: {min(scores)} 到 {max(scores)}")
    print()
    
    # 显示排序后的数据
    print("排序后的数据序列:")
    for i, (ts, score) in enumerate(zip(timestamps, scores)):
        print(f"{i+1:2d}. {ts.strftime('%m-%d %H:%M')} - 情绪分数: {score}")
    print()
    
    # 计算CEM
    result = calculate_cem_enhanced(scores, timestamps, '波动型')
    
    if 'error' in result:
        print(f"计算错误: {result['error']}")
        return
    
    # 输出结果
    print("=" * 40)
    print("计算结果")
    print("=" * 40)
    
    print(f"CEM值: {result['cem']}")
    print(f"信心度: {result['confidence']}")
    print(f"趋势强度: {result['trend_strength']}")
    print(f"时间敏感系数: {result['time_sensitivity']}")
    print()
    
    print("个人情绪基线:")
    baseline = result['baseline']
    print(f"  P25 (25%分位数): {baseline['P25']}")
    print(f"  P50 (中位数): {baseline['P50']}")
    print(f"  P75 (75%分位数): {baseline['P75']}")
    print()
    
    print("时间模式分析:")
    time_pattern = result['time_pattern']
    print(f"  平均时间间隔: {time_pattern['mean_interval']:.2f} 分钟")
    print(f"  时间间隔标准差: {time_pattern['std_interval']:.2f} 分钟")
    print(f"  时间变异系数: {time_pattern['cv_time']:.3f}")
    print(f"  时间类型: {time_pattern['time_type']}")
    print(f"  规律性评分: {time_pattern['regularity_score']}")
    print()
    
    print("详细计算过程:")
    details = result['calculation_details']
    print(f"  情绪类型调整系数: {details['type_adjustment']}")
    print(f"  相对位置序列: {details['relative_positions']}")
    print(f"  情绪变化序列: {details['emotional_changes']}")
    print(f"  时间权重序列: {details['weights_used']}")
    print()
    
    # 趋势解释
    cem_value = result['cem']
    trend_strength = result['trend_strength']
    
    print("=" * 40)
    print("结果解释")
    print("=" * 40)
    
    if cem_value > 0.5:
        trend_desc = "情绪上升趋势，关系向好发展"
    elif cem_value < -0.5:
        trend_desc = "情绪下降趋势，需要关注或干预"
    else:
        trend_desc = "情绪相对稳定，变化不明显"
    
    print(f"趋势方向: {trend_desc}")
    
    if trend_strength >= 0.75:
        strength_desc = "强烈趋势，需要重点关注"
    elif trend_strength >= 0.5:
        strength_desc = "中等趋势，适度干预"
    elif trend_strength >= 0.25:
        strength_desc = "轻微趋势，观察为主"
    else:
        strength_desc = "微弱趋势，维持现状"
    
    print(f"趋势强度: {strength_desc}")
    
    # 策略建议
    if cem_value > 0.5 and trend_strength > 0.5:
        strategy = "策略2：深化情感连接"
    elif cem_value < -0.5 and trend_strength > 0.75:
        strategy = "策略5：危机干预"
    elif cem_value < -0.5 and 0.25 <= trend_strength <= 0.75:
        strategy = "策略4：修复问题"
    elif abs(cem_value) < 0.3 and trend_strength < 0.25:
        strategy = "策略6：长期维护"
    else:
        strategy = "策略3：激活互动"
    
    print(f"建议策略: {strategy}")

if __name__ == "__main__":
    main()