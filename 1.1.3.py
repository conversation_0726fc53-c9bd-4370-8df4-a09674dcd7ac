# 数据充分性评估体系 - 1.1.3小节实现
# 基于质量评分的充分性标准，评估数据是否足够建立用户画像
# 遵循Python 3.11.3标准库限制，仅使用内置模块

import json
import math
from typing import Dict, List, Any, Union

async def main(args) -> Dict[str, Any]:
    """
    数据充分性评估主函数
    输入: args.params 包含评估所需的数据参数
    输出: 包含充分性评估结果的字典
    """
    try:
        # 获取输入参数
        params = args.params
        
        # 提取评估所需的数据 - 支持多种输入格式
        data_samples = []
        time_span_days = 0
        time_coverage_ratio = 0.0
        avg_quality_score = 0.0
        user_stability_score = 0.5
        
        # 处理直接参数输入
        if hasattr(params, 'get'):
            data_samples = params.get('data_samples', [])
            time_span_days = int(params.get('time_span_days', 0))
            time_coverage_ratio = float(params.get('time_coverage_ratio', 0.0))
            avg_quality_score = params.get('avg_quality_score', 0.0)
            user_stability_score = float(params.get('user_stability_score', 0.5))
        elif hasattr(params, '__getitem__'):
            # 处理字典类型参数
            data_samples = params.get('data_samples', [])
            time_span_days = int(params.get('time_span_days', 0))
            time_coverage_ratio = float(params.get('time_coverage_ratio', 0.0))
            avg_quality_score = params.get('avg_quality_score', 0.0)
            user_stability_score = float(params.get('user_stability_score', 0.5))
        
        # 处理嵌套input格式
        if hasattr(params, 'get') and 'input' in params:
            input_data = params.get('input', {})
            if isinstance(input_data, dict):
                data_samples = input_data.get('data_samples', data_samples)
                time_span_days = int(input_data.get('time_span_days', time_span_days))
                time_coverage_ratio = float(input_data.get('time_coverage_ratio', time_coverage_ratio))
                avg_quality_score = input_data.get('avg_quality_score', avg_quality_score)
                user_stability_score = float(input_data.get('user_stability_score', user_stability_score))
        
        # 处理字符串格式的质量评分
        if isinstance(avg_quality_score, str):
            try:
                avg_quality_score = float(avg_quality_score)
            except ValueError:
                avg_quality_score = 0.0
        elif not isinstance(avg_quality_score, (int, float)):
            avg_quality_score = 0.0
        
        # 数据充分性评估核心算法
        def calculate_data_volume_weight(sample_count: int) -> float:
            """计算数据量权重 (0-1)"""
            sample_count = max(0, int(sample_count))
            if sample_count >= 100:
                return 1.0
            elif sample_count >= 50:
                return 0.8
            elif sample_count >= 20:
                return 0.6
            else:
                return max(0.0, min(1.0, sample_count / 20.0))
        
        def calculate_time_span_weight(days: int) -> float:
            """计算时间跨度权重 (0-1)"""
            days = max(0, int(days))
            if days >= 60:
                return 1.0
            elif days >= 30:
                return 0.8
            elif days >= 14:
                return 0.6
            else:
                return max(0.0, min(1.0, days / 14.0))
        
        def calculate_coverage_weight(coverage_ratio: float) -> float:
            """计算时段覆盖权重 (0-1)"""
            coverage_ratio = max(0.0, min(1.0, float(coverage_ratio)))
            if coverage_ratio >= 0.95:
                return 1.0
            elif coverage_ratio >= 0.80:
                return 0.8
            elif coverage_ratio >= 0.60:
                return 0.6
            else:
                return coverage_ratio
        
        def calculate_quality_weight(avg_score: float) -> float:
            """计算质量评分权重 (0-1)"""
            avg_score = max(0.0, min(10.0, float(avg_score)))
            if avg_score >= 8.0:
                return 1.0
            elif avg_score >= 7.0:
                return 0.8
            elif avg_score >= 6.0:
                return 0.6
            else:
                return max(0.0, min(1.0, avg_score / 6.0))
        
        def calculate_sufficiency_threshold(stability_score: float) -> float:
            """动态充分性标准调整"""
            stability_score = max(0.0, min(1.0, float(stability_score)))
            base_threshold = 0.6
            
            if stability_score > 0.8:
                threshold_adjustment = -0.1  # 稳定用户可降低要求
            elif stability_score < 0.4:
                threshold_adjustment = 0.2   # 不稳定用户需更多数据
            else:
                threshold_adjustment = 0.0
            
            return max(0.4, min(0.9, base_threshold + threshold_adjustment))
        
        def determine_sufficiency_level(sufficiency_index: float) -> tuple:
            """判断充分性等级"""
            sufficiency_index = max(0.0, min(1.0, float(sufficiency_index)))
            if sufficiency_index >= 0.8:
                return "充分", "立即建立画像", "高置信度画像", True
            elif sufficiency_index >= 0.6:
                return "基本充分", "可建立初步画像", "中等置信度画像", True
            elif sufficiency_index >= 0.4:
                return "不充分", "继续收集数据", "延迟画像建立", False
            else:
                return "严重不足", "重新评估收集策略", "暂停画像建立", False
        
        # 计算各维度权重
        sample_count = len(data_samples) if isinstance(data_samples, list) else 0
        data_volume_weight = calculate_data_volume_weight(sample_count)
        time_span_weight = calculate_time_span_weight(time_span_days)
        coverage_weight = calculate_coverage_weight(time_coverage_ratio)
        quality_weight = calculate_quality_weight(avg_quality_score)
        
        # 数据充分性指数计算
        # 公式：数据量权重×0.4 + 时间跨度权重×0.3 + 时段覆盖权重×0.15 + 质量评分权重×0.15
        sufficiency_index = (
            data_volume_weight * 0.4 +
            time_span_weight * 0.3 +
            coverage_weight * 0.15 +
            quality_weight * 0.15
        )
        
        # 动态充分性阈值
        dynamic_threshold = calculate_sufficiency_threshold(user_stability_score)
        
        # 充分性判断
        sufficiency_level, recommended_action, portrait_strategy, can_build_portrait = determine_sufficiency_level(sufficiency_index)
        
        # 是否满足动态阈值
        meets_threshold = sufficiency_index >= dynamic_threshold
        
        # 详细评估报告
        assessment_details = {
            "data_volume_assessment": {
                "sample_count": sample_count,
                "weight_score": round(data_volume_weight, 3),
                "standard": "优秀" if sample_count >= 100 else "推荐" if sample_count >= 50 else "最小" if sample_count >= 20 else "不足"
            },
            "time_span_assessment": {
                "days": time_span_days,
                "weight_score": round(time_span_weight, 3),
                "standard": "优秀" if time_span_days >= 60 else "推荐" if time_span_days >= 30 else "最小" if time_span_days >= 14 else "不足"
            },
            "coverage_assessment": {
                "coverage_ratio": round(time_coverage_ratio, 3),
                "weight_score": round(coverage_weight, 3),
                "standard": "优秀" if time_coverage_ratio >= 0.95 else "推荐" if time_coverage_ratio >= 0.80 else "最小" if time_coverage_ratio >= 0.60 else "不足"
            },
            "quality_assessment": {
                "avg_score": round(avg_quality_score, 2),
                "weight_score": round(quality_weight, 3),
                "standard": "优秀" if avg_quality_score >= 8.0 else "推荐" if avg_quality_score >= 7.0 else "最小" if avg_quality_score >= 6.0 else "不足"
            }
        }
        
        # 改进建议
        improvement_suggestions = []
        if data_volume_weight < 0.8:
            target_samples = 50 if sample_count < 50 else 100
            improvement_suggestions.append(f"建议增加数据样本至{target_samples}个以上")
        if time_span_weight < 0.8:
            target_days = 30 if time_span_days < 30 else 60
            improvement_suggestions.append(f"建议延长数据收集时间至{target_days}天以上")
        if coverage_weight < 0.8:
            improvement_suggestions.append("建议提高时段覆盖率至80%以上")
        if quality_weight < 0.8:
            improvement_suggestions.append("建议提升数据质量评分至7.0分以上")
        
        # 风险评估
        risk_factors = []
        if sufficiency_index < 0.5:
            risk_factors.append("数据不足可能导致画像偏差")
        if user_stability_score < 0.4:
            risk_factors.append("用户情绪不稳定，需要更多观察期")
        if avg_quality_score < 6.0:
            risk_factors.append("数据质量偏低，可能影响分析准确性")
        
        # 置信度计算
        confidence_score = min(1.0, sufficiency_index * (1 + user_stability_score * 0.2))
        
        # 预估完成时间计算
        estimated_days = 0
        if sufficiency_index < dynamic_threshold:
            gap = dynamic_threshold - sufficiency_index
            estimated_days = max(0, int(gap * 30))
        
        # 返回评估结果
        return {
            "sufficiency_index": round(sufficiency_index, 3),
            "sufficiency_level": sufficiency_level,
            "recommended_action": recommended_action,
            "portrait_strategy": portrait_strategy,
            "can_build_portrait": can_build_portrait,
            "meets_threshold": meets_threshold,
            "dynamic_threshold": round(dynamic_threshold, 3),
            "confidence_score": round(confidence_score, 3),
            "assessment_details": assessment_details,
            "improvement_suggestions": improvement_suggestions,
            "risk_factors": risk_factors,
            "user_stability_impact": {
                "stability_score": round(user_stability_score, 3),
                "threshold_adjustment": round(dynamic_threshold - 0.6, 3),
                "impact_description": "稳定用户，降低要求" if user_stability_score > 0.8 else "不稳定用户，提高要求" if user_stability_score < 0.4 else "普通用户，标准要求"
            },
            "next_steps": {
                "immediate_action": recommended_action,
                "data_collection_priority": "高" if sufficiency_index < 0.6 else "中" if sufficiency_index < 0.8 else "低",
                "estimated_days_to_sufficiency": estimated_days
            }
        }
        
    except Exception as e:
        # 错误处理
        return {
            "sufficiency_index": 0.0,
            "sufficiency_level": "评估失败",
            "recommended_action": "检查输入数据",
            "portrait_strategy": "暂停处理",
            "can_build_portrait": False,
            "meets_threshold": False,
            "dynamic_threshold": 0.6,
            "confidence_score": 0.0,
            "error": str(e),
            "status": "failed"
        }