import json
from datetime import datetime
from typing import Dict, List, Any, Union
import math

async def main(args: Args) -> Output:
    params = args.params
    
    try:
        # 获取输入参数
        output_list = params.get('outputList', [])
        rsi_value = float(params.get('rsi_value', '0.5'))
        ei_value = float(params.get('ei_value', '0.5'))
        cem_value = float(params.get('cem_value', '0.5'))
        # 推断用户类型（基于其他计算模块的结果）
        user_type = infer_user_type(params)
        s_stability_factor = float(params.get('s_stability_factor', '0.5'))
        m_stability_factor = float(params.get('m_stability_factor', '0.5'))
        t_stability_factor = float(params.get('t_stability_factor', '0.5'))
        
        # 新增的输入参数
        final_p25 = float(params.get('final_P25', '3.0'))
        final_p50 = float(params.get('final_P50', '5.0'))
        final_p75 = float(params.get('final_P75', '7.0'))
        confidence_level = float(params.get('confidence_level', '0.7'))
        ei_confidence = float(params.get('ei_confidence', '0.7'))
        cem_confidence = float(params.get('cem_confidence', '0.7'))
        coordination_index = float(params.get('coordination_index', '0.6'))
        
        # 提取历史情绪序列
        emotion_sequence = []
        time_sequence = []
        word_counts = []

        for item in output_list:
            if isinstance(item, dict):
                # 跳过空值项（如最后一项）
                if (item.get('P25') == '' or item.get('P50') == '' or item.get('P75') == '' or
                    item.get('emo_value') is None or item.get('number') is None):
                    continue

                emo_val = float(item.get('emo_value', 5.0))
                emotion_sequence.append(emo_val)

                time_str = item.get('bstudio_create_time', '')
                time_sequence.append(time_str)

                word_count = int(item.get('number', 0))
                word_counts.append(word_count)
        
        # 数据质量评估和门槛策略
        data_points_count = len(emotion_sequence)
        calculation_mode, confidence_multiplier = get_calculation_mode(data_points_count)
        
        # 计算EII情绪惯性指数
        eii_result = calculate_eii_index(
            emotion_sequence=emotion_sequence,
            time_sequence=time_sequence,
            word_counts=word_counts,
            s_stability_factor=s_stability_factor,
            m_stability_factor=m_stability_factor,
            t_stability_factor=t_stability_factor,
            rsi_value=rsi_value,
            ei_value=ei_value,
            cem_value=cem_value,
            final_p25=final_p25,
            final_p50=final_p50,
            final_p75=final_p75,
            coordination_index=coordination_index,
            user_type=user_type,
            calculation_mode=calculation_mode
        )
        
        # 计算置信度
        confidence_result = calculate_confidence(
            confidence_level=confidence_level,
            ei_confidence=ei_confidence,
            cem_confidence=cem_confidence,
            data_points_count=data_points_count,
            calculation_mode=calculation_mode,
            confidence_multiplier=confidence_multiplier
        )
        
        # 为后续模块准备专业化数据
        crisis_data = prepare_crisis_assessment_data(eii_result)
        health_data = prepare_health_assessment_data(eii_result)
        
        # 构建标准化输出（确保所有数值都是Python原生类型）
        ret: Output = {
            "calculation_id": f"eii_calc_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "calculation_type": "emotional_inertia",
            "version": "5.1.0",
            "timestamp": datetime.now().isoformat(),
            
            "eii_assessment": {
                "eii_value": float(round(eii_result['eii_value'], 3)),
                "inertia_grade": str(eii_result['inertia_grade']),
                "inertia_pattern": str(eii_result['inertia_pattern']),
                "adaptability_score": float(round(eii_result['adaptability_score'], 3)),
                "overall_evaluation": str(eii_result['overall_evaluation'])
            },
            
            "inertia_analysis": {
                "s_inertia_factor": float(round(eii_result['s_inertia_factor'], 3)),
                "m_inertia_factor": float(round(eii_result['m_inertia_factor'], 3)),
                "t_inertia_factor": float(round(eii_result['t_inertia_factor'], 3)),
                "dominant_dimension": str(eii_result['dominant_dimension']),
                "inertia_balance": float(round(eii_result['inertia_balance'], 3)),
                "base_eii": float(round(eii_result['base_eii'], 3)),
                "final_eii": float(round(eii_result['eii_value'], 3))
            },
            
            "pattern_characteristics": {
                "pattern_type": str(eii_result['inertia_pattern']),
                "pattern_stability": str(eii_result['pattern_stability']),
                "flexibility_level": str(eii_result['flexibility_level']),
                "adaptation_capability": str(eii_result['adaptation_capability']),
                "key_features": [str(f) for f in eii_result['key_features']]
            },
            
            "dynamic_assessment": {
                "inertia_trend": str(eii_result['inertia_trend']),
                "change_velocity": float(eii_result['change_velocity']),
                "regression_tendency": float(round(eii_result['regression_tendency'], 3)),
                "adjustment_capacity": float(round(eii_result['adjustment_capacity'], 3)),
                "development_direction": str(eii_result['development_direction'])
            },
            
            "for_crisis_assessment": crisis_data,
            "for_health_evaluation": health_data,
            
            "confidence_breakdown": {
                "l1_input_quality": float(round(confidence_result['l1_input_quality'], 3)),
                "l2_calculation_stability": float(round(confidence_result['l2_calculation_stability'], 3)),
                "l3_output_confidence": float(round(confidence_result['l3_output_confidence'], 3))
            },
            
            "validation_result": {
                "eii_validation": bool(eii_result['eii_validation']),
                "pattern_consistency": bool(eii_result['pattern_consistency']),
                "temporal_consistency": float(round(eii_result['temporal_consistency'], 3)),
                "adaptability_assessment": bool(eii_result['adaptability_assessment']),
                "anomaly_detected": bool(eii_result['anomaly_detected'])
            },
            
            "metadata": {
                "data_points_used": int(data_points_count),
                "time_span_days": int(calculate_time_span(time_sequence)),
                "user_type": str(user_type),
                "calculation_mode": str(calculation_mode),
                "baseline_range": float(round(final_p75 - final_p25, 2)),
                "processing_time_ms": int(75),
                "quality_grade": str(get_quality_grade(confidence_result['l3_output_confidence'])),
                "unique_output": "情绪惯性动态分析"
            }
        }
        
        return ret
        
    except Exception as e:
        return {
            "error": True,
            "error_message": str(e),
            "calculation_type": "emotional_inertia",
            "timestamp": datetime.now().isoformat()
        }

def to_python_type(value: Any) -> Any:
    """将NumPy类型转换为Python原生类型"""
    if hasattr(value, 'item'):  # NumPy scalar
        return value.item()
    elif hasattr(value, 'tolist'):  # NumPy array
        return value.tolist()
    else:
        return value

def get_calculation_mode(data_points_count: int) -> tuple:
    """渐进式数据门槛策略"""
    if data_points_count >= 7:
        return "完整惯性计算", 1.0
    elif data_points_count >= 4:
        return "简化惯性计算", 0.8
    elif data_points_count >= 2:
        return "基础惯性估算", 0.6
    else:
        return "类型默认惯性", 0.4

def calculate_eii_index(emotion_sequence, time_sequence, word_counts, s_stability_factor, 
                       m_stability_factor, t_stability_factor, rsi_value, ei_value, cem_value,
                       final_p25, final_p50, final_p75, coordination_index, user_type, calculation_mode):
    """计算EII情绪惯性指数"""
    
    cultural_adaptation = get_cultural_adaptation_factor(user_type)
    baseline_range = final_p75 - final_p25
    baseline_center = final_p50
    
    # 计算三个惯性因子
    s_inertia = calculate_s_inertia_factor(emotion_sequence, s_stability_factor, baseline_center, cultural_adaptation, calculation_mode)
    m_inertia = calculate_m_inertia_factor(emotion_sequence, word_counts, m_stability_factor, cultural_adaptation, calculation_mode)
    t_inertia = calculate_t_inertia_factor(time_sequence, t_stability_factor, cultural_adaptation, calculation_mode)
    
    # 基础EII计算
    base_eii = 0.45 * s_inertia + 0.35 * m_inertia + 0.20 * t_inertia
    
    # 各种调整
    stability_adjustment = get_stability_adjustment(rsi_value)
    coordination_correction = 0.9 + coordination_index * 0.2
    type_correction = get_user_type_correction(user_type)
    
    # 最终EII值
    final_eii = min(1.0, max(0.0, base_eii * stability_adjustment * coordination_correction * type_correction))
    
    # 其他计算
    adaptability_score = calculate_adaptability_score(s_inertia, m_inertia, t_inertia, user_type, baseline_range)
    pattern_result = identify_inertia_pattern(s_inertia, m_inertia, t_inertia)
    inertia_grade = classify_eii_grade(final_eii)
    temporal_consistency = calculate_temporal_consistency(emotion_sequence)
    regression_tendency = calculate_regression_tendency(emotion_sequence, baseline_center)
    
    # 确保所有返回值都是Python原生类型
    return {
        "eii_value": to_python_type(final_eii),
        "inertia_grade": inertia_grade,
        "inertia_pattern": pattern_result['pattern_type'],
        "adaptability_score": to_python_type(adaptability_score),
        "overall_evaluation": get_overall_evaluation(final_eii, adaptability_score),
        "s_inertia_factor": to_python_type(s_inertia),
        "m_inertia_factor": to_python_type(m_inertia),
        "t_inertia_factor": to_python_type(t_inertia),
        "dominant_dimension": pattern_result['dominant_dimension'],
        "inertia_balance": to_python_type(pattern_result['inertia_balance']),
        "base_eii": to_python_type(base_eii),
        "pattern_stability": pattern_result['pattern_stability'],
        "flexibility_level": get_flexibility_level(adaptability_score),
        "adaptation_capability": get_adaptation_capability(adaptability_score),
        "key_features": generate_key_features(s_inertia, m_inertia, t_inertia),
        "inertia_trend": analyze_inertia_trend(emotion_sequence),
        "change_velocity": to_python_type(calculate_change_velocity(emotion_sequence)),
        "regression_tendency": to_python_type(regression_tendency),
        "adjustment_capacity": to_python_type(adaptability_score),
        "development_direction": get_development_direction(final_eii, adaptability_score),
        "eii_validation": to_python_type(validate_eii_range(final_eii)),
        "pattern_consistency": to_python_type(pattern_result['pattern_consistency']),
        "temporal_consistency": to_python_type(temporal_consistency),
        "adaptability_assessment": to_python_type(adaptability_score > 0.5),
        "anomaly_detected": to_python_type(detect_anomaly(final_eii, adaptability_score))
    }

def calculate_s_inertia_factor(emotion_sequence, s_stability_factor, baseline_center, cultural_adaptation, calculation_mode):
    """计算S参数惯性因子"""
    if len(emotion_sequence) < 2:
        # 根据计算模式返回不同默认值
        if calculation_mode == "类型默认惯性":
            return s_stability_factor  # 使用稳定性因子作为默认值
        else:
            return 0.5

    persistence = calculate_robust_autocorrelation(emotion_sequence, lag=1)
    stability_base = s_stability_factor
    regression = calculate_baseline_regression(emotion_sequence, baseline_center)
    adaptability = calculate_emotion_adaptability(emotion_sequence)

    # 修正权重分配：持续性0.4 + 稳定性0.3 + 回归性0.2 + 适应性0.1
    s_inertia = (persistence * 0.4 + stability_base * 0.3 + regression * 0.2 + adaptability * 0.1) * cultural_adaptation
    return min(1.0, max(0.0, s_inertia))

def calculate_m_inertia_factor(emotion_sequence, word_counts, m_stability_factor, cultural_adaptation, calculation_mode):
    """计算M参数惯性因子"""
    if len(emotion_sequence) < 2:
        # 根据计算模式返回不同默认值
        if calculation_mode == "类型默认惯性":
            return m_stability_factor  # 使用稳定性因子作为默认值
        else:
            return 0.5

    investment_persistence = calculate_robust_autocorrelation(emotion_sequence, lag=1) * 0.9
    investment_stability = m_stability_factor

    if len(word_counts) > 1:
        word_mean = sum(word_counts) / len(word_counts)
        word_std = (sum((x - word_mean) ** 2 for x in word_counts) / len(word_counts)) ** 0.5
        word_consistency = 1.0 - (word_std / (word_mean + 1))
        word_consistency = max(0.0, min(1.0, word_consistency))
    else:
        word_consistency = 0.5

    investment_adaptability = calculate_investment_adaptability(emotion_sequence, word_counts)
    m_inertia = (investment_persistence * 0.3 + investment_stability * 0.3 + word_consistency * 0.25 + investment_adaptability * 0.15) * cultural_adaptation
    return min(1.0, max(0.0, m_inertia))

def calculate_t_inertia_factor(time_sequence, t_stability_factor, cultural_adaptation, calculation_mode):
    """计算T参数惯性因子"""
    if len(time_sequence) < 2:
        # 根据计算模式返回不同默认值
        if calculation_mode == "类型默认惯性":
            return t_stability_factor  # 使用稳定性因子作为默认值
        else:
            return 0.5

    time_regularity = 0.7
    time_stability = t_stability_factor
    response_timeliness = 0.8
    time_adaptability = 0.6

    t_inertia = (time_regularity * 0.25 + time_stability * 0.25 + response_timeliness * 0.25 + time_adaptability * 0.25) * cultural_adaptation
    return min(1.0, max(0.0, t_inertia))

def calculate_robust_autocorrelation(sequence, lag=1):
    """鲁棒自相关计算"""
    if len(sequence) < lag + 3:
        return 0.5

    try:
        x = sequence[:-lag]
        y = sequence[lag:]

        if len(x) < 3:
            return 0.5

        # 手动计算相关系数避免NumPy类型问题
        x_mean = sum(x) / len(x)
        y_mean = sum(y) / len(y)

        numerator = sum((x[i] - x_mean) * (y[i] - y_mean) for i in range(len(x)))
        x_var = sum((x[i] - x_mean) ** 2 for i in range(len(x)))
        y_var = sum((y[i] - y_mean) ** 2 for i in range(len(y)))

        if x_var == 0 or y_var == 0:
            return 0.5

        correlation = numerator / (x_var * y_var) ** 0.5

        if correlation != correlation:  # 检查NaN
            return 0.5

        return max(0.0, min(1.0, (correlation + 1) / 2))
    except:
        return 0.5

def calculate_baseline_regression(emotion_sequence, baseline_center):
    """计算基线回归趋势"""
    if len(emotion_sequence) < 2:
        return 0.5

    deviations = [abs(emo - baseline_center) for emo in emotion_sequence]

    if len(deviations) < 2:
        return 0.5

    try:
        # 手动计算线性趋势
        n = len(deviations)
        x_vals = list(range(n))
        x_mean = sum(x_vals) / n
        y_mean = sum(deviations) / n

        numerator = sum((x_vals[i] - x_mean) * (deviations[i] - y_mean) for i in range(n))
        denominator = sum((x_vals[i] - x_mean) ** 2 for i in range(n))

        if denominator == 0:
            return 0.5

        trend = numerator / denominator
        regression_score = max(0.0, min(1.0, 0.5 - trend * 0.1))
        return regression_score
    except:
        return 0.5

def calculate_emotion_adaptability(emotion_sequence):
    """计算情绪适应性"""
    if len(emotion_sequence) < 2:
        return 0.5

    # 手动计算标准差
    mean_val = sum(emotion_sequence) / len(emotion_sequence)
    variance = sum((x - mean_val) ** 2 for x in emotion_sequence) / len(emotion_sequence)
    emotion_std = variance ** 0.5

    adaptability = max(0.0, min(1.0, 1.0 - emotion_std / 5.0))
    return adaptability

def calculate_investment_adaptability(emotion_sequence, word_counts):
    """计算投入适应性"""
    if len(emotion_sequence) < 2 or len(word_counts) < 2:
        return 0.5

    emotion_adaptability = calculate_emotion_adaptability(emotion_sequence)

    # 手动计算字数标准差
    word_mean = sum(word_counts) / len(word_counts)
    word_variance = sum((x - word_mean) ** 2 for x in word_counts) / len(word_counts)
    word_std = word_variance ** 0.5
    word_adaptability = max(0.0, min(1.0, 1.0 - word_std / 10.0))

    return (emotion_adaptability + word_adaptability) / 2

def calculate_adaptability_score(s_inertia, m_inertia, t_inertia, user_type, baseline_range):
    """计算适应性评分"""
    ideal_range = get_personalized_ideal_range(user_type, baseline_range)

    s_adaptability = calculate_dimension_adaptability(s_inertia, ideal_range)
    m_adaptability = calculate_dimension_adaptability(m_inertia, ideal_range)
    t_adaptability = calculate_dimension_adaptability(t_inertia, ideal_range)

    adaptability = (s_adaptability * 0.4 + m_adaptability * 0.35 + t_adaptability * 0.25)
    return min(1.0, max(0.0, adaptability))

def get_personalized_ideal_range(user_type, baseline_range):
    """获取个性化理想惯性范围"""
    base_ranges = {
        '积极稳定型': (0.6, 0.8),
        '沉稳内敛型': (0.7, 0.9),
        '情绪敏感型': (0.3, 0.5),
        '消极波动型': (0.4, 0.6),
        '适应调整型': (0.4, 0.7)
    }

    base_range = base_ranges.get(user_type, (0.5, 0.7))

    if baseline_range > 3.0:
        adjustment = -0.1
    elif baseline_range < 1.5:
        adjustment = 0.1
    else:
        adjustment = 0.0

    adjusted_range = (max(0.1, base_range[0] + adjustment), min(1.0, base_range[1] + adjustment))
    return adjusted_range

def calculate_dimension_adaptability(inertia_value, ideal_range):
    """计算维度适应性"""
    lower, upper = ideal_range

    if lower <= inertia_value <= upper:
        return 1.0

    center = (lower + upper) / 2
    width = upper - lower
    sigma = width / 4
    distance = min(abs(inertia_value - lower), abs(inertia_value - upper))

    # 手动计算exp函数
    return math.exp(-(distance ** 2) / (2 * sigma ** 2))

def identify_inertia_pattern(s_inertia, m_inertia, t_inertia):
    """识别惯性模式"""
    inertia_values = [s_inertia, m_inertia, t_inertia]
    avg_inertia = sum(inertia_values) / len(inertia_values)

    # 手动计算方差
    variance_sum = sum((x - avg_inertia) ** 2 for x in inertia_values)
    inertia_variance = variance_sum / len(inertia_values)

    variance_threshold = 0.08

    if inertia_variance < variance_threshold:
        if avg_inertia > 0.75:
            pattern = "高协调稳定型"
        elif avg_inertia > 0.55:
            pattern = "中协调平衡型"
        elif avg_inertia > 0.35:
            pattern = "低协调灵活型"
        else:
            pattern = "极低协调不稳定型"
    else:
        dominant_dim = get_dominant_dimension(s_inertia, m_inertia, t_inertia)
        pattern = f"{dominant_dim}主导分化型"

    return {
        "pattern_type": pattern,
        "dominant_dimension": get_dominant_dimension(s_inertia, m_inertia, t_inertia),
        "inertia_balance": max(0.0, 1 - (inertia_variance / variance_threshold)),
        "pattern_stability": "稳定" if inertia_variance < variance_threshold * 0.5 else "波动",
        "pattern_consistency": True
    }

def get_dominant_dimension(s_inertia, m_inertia, t_inertia):
    """获取主导维度"""
    if s_inertia > max(m_inertia, t_inertia) + 0.15:
        return "情绪惯性"
    elif m_inertia > max(s_inertia, t_inertia) + 0.15:
        return "投入惯性"
    elif t_inertia > max(s_inertia, m_inertia) + 0.15:
        return "时间惯性"
    else:
        return "平衡型"

def classify_eii_grade(eii_value):
    """EII等级分类"""
    if eii_value >= 0.8:
        return "优秀惯性"
    elif eii_value >= 0.6:
        return "良好惯性"
    elif eii_value >= 0.4:
        return "一般惯性"
    elif eii_value >= 0.2:
        return "较差惯性"
    else:
        return "异常惯性"

def calculate_temporal_consistency(emotion_sequence):
    """计算时间一致性"""
    if len(emotion_sequence) < 3:
        return 0.5

    # 手动计算差分和标准差
    differences = [emotion_sequence[i+1] - emotion_sequence[i] for i in range(len(emotion_sequence)-1)]
    abs_differences = [abs(d) for d in differences]

    if len(differences) == 0:
        return 0.5

    diff_mean = sum(differences) / len(differences)
    diff_variance = sum((d - diff_mean) ** 2 for d in differences) / len(differences)
    diff_std = diff_variance ** 0.5

    abs_diff_mean = sum(abs_differences) / len(abs_differences)

    smoothness = 1.0 - (diff_std / (abs_diff_mean + 0.1))
    return max(0.0, min(1.0, smoothness))

def calculate_regression_tendency(emotion_sequence, baseline_center):
    """计算回归趋势"""
    return calculate_baseline_regression(emotion_sequence, baseline_center)

def calculate_confidence(confidence_level, ei_confidence, cem_confidence, data_points_count, calculation_mode, confidence_multiplier):
    """计算三层置信度"""
    l1_confidence = (confidence_level * 0.4 + ei_confidence * 0.3 + cem_confidence * 0.3) * confidence_multiplier

    data_sufficiency = min(1.0, data_points_count / 7.0)
    calculation_stability = {"完整惯性计算": 0.9, "简化惯性计算": 0.8, "基础惯性估算": 0.6, "类型默认惯性": 0.4}.get(calculation_mode, 0.7)

    l2_confidence = data_sufficiency * calculation_stability
    l3_confidence = (l1_confidence ** 0.6) * (l2_confidence ** 0.4) * 0.95

    return {"l1_input_quality": l1_confidence, "l2_calculation_stability": l2_confidence, "l3_output_confidence": l3_confidence}

def prepare_crisis_assessment_data(eii_result):
    """为计算6准备危机评估数据"""
    eii_value = eii_result['eii_value']
    adaptability_score = eii_result['adaptability_score']

    if eii_value > 0.8:
        inertia_stability_risk = (eii_value - 0.8) / 0.2
    elif eii_value < 0.3:
        inertia_stability_risk = (0.3 - eii_value) / 0.3
    else:
        inertia_stability_risk = 0.0

    return {
        "inertia_stability_risk": float(round(min(1.0, inertia_stability_risk), 3)),
        "adaptation_failure_risk": float(round(max(0.0, 1.0 - adaptability_score), 3)),
        "emotional_rigidity_score": float(round(max(0.0, (eii_value - 0.7) / 0.3), 3)),
        "change_resistance_level": float(round(eii_value * 0.8, 3))
    }

def prepare_health_assessment_data(eii_result):
    """为计算7准备健康评估数据"""
    adaptability_score = eii_result['adaptability_score']
    inertia_balance = eii_result['inertia_balance']

    return {
        "adaptive_capacity_score": float(round(adaptability_score, 3)),
        "emotional_flexibility_index": float(round(inertia_balance * adaptability_score, 3)),
        "growth_potential_indicator": float(round(min(1.0, adaptability_score * 1.2), 3)),
        "resilience_factor": float(round((adaptability_score + inertia_balance) / 2, 3))
    }

def get_cultural_adaptation_factor(user_type):
    """获取文化适配系数"""
    adaptations = {'积极稳定型': 1.0, '沉稳内敛型': 0.95, '情绪敏感型': 0.9, '消极波动型': 0.85, '适应调整型': 0.92}
    return adaptations.get(user_type, 0.92)

def get_stability_adjustment(rsi_value):
    """获取稳定性调整系数"""
    if rsi_value < 0.2:
        return 1.5
    elif rsi_value < 0.4:
        return 1.2
    else:
        return 1.0

def get_user_type_correction(user_type):
    """获取用户类型校正系数"""
    corrections = {'积极稳定型': 1.0, '沉稳内敛型': 1.1, '情绪敏感型': 0.9, '消极波动型': 0.85, '适应调整型': 0.95}
    return corrections.get(user_type, 1.0)

def calculate_time_span(time_sequence):
    """计算时间跨度"""
    return max(1, len(time_sequence))

def get_quality_grade(confidence):
    """获取质量等级"""
    if confidence >= 0.8:
        return "高"
    elif confidence >= 0.6:
        return "中"
    else:
        return "低"

def get_overall_evaluation(eii_value, adaptability_score):
    """获取整体评价"""
    if eii_value >= 0.7 and adaptability_score >= 0.7:
        return "健康的情绪惯性"
    elif eii_value >= 0.5 and adaptability_score >= 0.5:
        return "基本健康的情绪惯性"
    else:
        return "需要关注的情绪惯性"

def get_flexibility_level(adaptability_score):
    """获取灵活性水平"""
    if adaptability_score >= 0.8:
        return "高"
    elif adaptability_score >= 0.6:
        return "适中"
    else:
        return "低"

def get_adaptation_capability(adaptability_score):
    """获取适应能力"""
    if adaptability_score >= 0.8:
        return "优秀"
    elif adaptability_score >= 0.6:
        return "良好"
    elif adaptability_score >= 0.4:
        return "一般"
    else:
        return "较差"

def generate_key_features(s_inertia, m_inertia, t_inertia):
    """生成关键特征"""
    features = []
    if s_inertia > 0.7:
        features.append("情绪稳定性强")
    if m_inertia > 0.7:
        features.append("投入持续性高")
    if t_inertia > 0.7:
        features.append("时间规律性好")
    if not features:
        features.append("惯性特征均衡")
    return features

def analyze_inertia_trend(emotion_sequence):
    """分析惯性趋势"""
    if len(emotion_sequence) < 3:
        return "数据不足"

    recent_avg = sum(emotion_sequence[-3:]) / 3
    early_avg = sum(emotion_sequence[:3]) / 3

    if recent_avg > early_avg + 0.5:
        return "上升趋势"
    elif recent_avg < early_avg - 0.5:
        return "下降趋势"
    else:
        return "稳定趋势"

def calculate_change_velocity(emotion_sequence):
    """计算变化速度"""
    if len(emotion_sequence) < 2:
        return 0.0

    changes = [abs(emotion_sequence[i+1] - emotion_sequence[i]) for i in range(len(emotion_sequence)-1)]
    return round(sum(changes) / len(changes), 3)

def get_development_direction(eii_value, adaptability_score):
    """获取发展方向"""
    if eii_value > 0.8:
        return "需要增加灵活性"
    elif eii_value < 0.3:
        return "需要增强稳定性"
    elif adaptability_score < 0.5:
        return "需要提升适应能力"
    else:
        return "保持当前状态"

def validate_eii_range(eii_value):
    """验证EII范围"""
    return 0.0 <= eii_value <= 1.0

def detect_anomaly(eii_value, adaptability_score):
    """检测异常"""
    return eii_value < 0.1 or eii_value > 0.95 or adaptability_score < 0.1

def infer_user_type(params):
    """基于输入参数推断用户类型"""
    # 基于RSI、EI、CEM值推断用户类型
    rsi_value = float(params.get('rsi_value', '0.5'))
    ei_value = float(params.get('ei_value', '0.5'))
    cem_value = float(params.get('cem_value', '0.5'))

    # 综合评分
    stability_score = (rsi_value + ei_value) / 2
    emotion_score = cem_value

    if stability_score >= 0.7 and emotion_score >= 0.7:
        return '积极稳定型'
    elif stability_score >= 0.6 and emotion_score >= 0.4:
        return '沉稳内敛型'
    elif stability_score < 0.4 and emotion_score >= 0.6:
        return '情绪敏感型'
    elif stability_score < 0.4 and emotion_score < 0.4:
        return '消极波动型'
    else:
        return '适应调整型'
