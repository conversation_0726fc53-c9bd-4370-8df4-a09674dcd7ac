#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1.1.3 数据充分性评估体系
基于经典测试理论和项目反应理论的科学数据充分性评估标准
实现DSI计算、分层存储、智能数据选取等核心功能
"""

import json
import math
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict
import statistics

@dataclass
class DataRecord:
    """数据记录结构"""
    user_id: str
    content: str
    emotion_score: float
    quality_score: float
    timestamp: datetime
    word_count: int
    data_id: str = ""
    
@dataclass
class SufficiencyResult:
    """充分性评估结果"""
    dsi_score: float
    sufficiency_level: str
    recommendation: str
    component_scores: Dict[str, float]
    threshold_used: float
    can_build_profile: bool

class DataSufficiencyEvaluator:
    """数据充分性评估器"""
    
    def __init__(self):
        # DSI权重配置
        self.dsi_weights = {
            'data_volume': 0.4,
            'time_span': 0.3, 
            'time_coverage': 0.15,
            'quality_score': 0.15
        }
        
        # 充分性阈值标准
        self.sufficiency_thresholds = {
            'sufficient': 0.8,
            'basic_sufficient': 0.6,
            'insufficient': 0.4
        }
        
        # 分层存储配置
        self.memory_layers = {
            'working': {'days': 7, 'capacity': 50, 'weight': 1.0},
            'short_term': {'days': 28, 'capacity': 150, 'weight': 0.8},
            'long_term': {'days': 112, 'capacity': 300, 'weight': 1.0},
            'core': {'days': float('inf'), 'capacity': 100, 'weight': 0.4},
            'personal': {'days': float('inf'), 'capacity': 200, 'weight': 0.8}
        }
    
    def calculate_data_volume_score(self, data_count: int) -> float:
        """计算数据量得分 - S型函数"""
        # f_N(x) = 1/(1+e^(-0.1*(x-50)))
        try:
            score = 1.0 / (1.0 + math.exp(-0.1 * (data_count - 50)))
            return min(1.0, max(0.0, score))
        except (OverflowError, ZeroDivisionError):
            return 1.0 if data_count >= 50 else 0.0
    
    def calculate_time_span_score(self, days: int) -> float:
        """计算时间跨度得分 - 线性函数"""
        # f_T(x) = min(x/60, 1.0)
        return min(days / 60.0, 1.0)
    
    def calculate_time_coverage_score(self, data_records: List[DataRecord]) -> float:
        """计算时段覆盖得分"""
        if not data_records:
            return 0.0
        
        # 按小时统计覆盖情况
        hours_covered = set()
        for record in data_records:
            hours_covered.add(record.timestamp.hour)
        
        # 计算覆盖率
        coverage_rate = len(hours_covered) / 24.0
        return coverage_rate
    
    def calculate_quality_score(self, data_records: List[DataRecord]) -> float:
        """计算质量得分"""
        if not data_records:
            return 0.0
        
        # 计算平均质量分数
        avg_quality = statistics.mean([r.quality_score for r in data_records])
        
        # 标准化函数：f_Q(x) = (x-4)/6，将4-10分映射到0-1
        normalized_score = (avg_quality - 4.0) / 6.0
        return min(1.0, max(0.0, normalized_score))
    
    def calculate_dsi(self, data_records: List[DataRecord]) -> Tuple[float, Dict[str, float]]:
        """计算数据充分性指数(DSI)"""
        if not data_records:
            return 0.0, {}
        
        # 计算各维度得分
        data_count = len(data_records)
        
        # 计算时间跨度
        timestamps = [r.timestamp for r in data_records]
        time_span_days = (max(timestamps) - min(timestamps)).days + 1
        
        # 各维度得分
        volume_score = self.calculate_data_volume_score(data_count)
        time_score = self.calculate_time_span_score(time_span_days)
        coverage_score = self.calculate_time_coverage_score(data_records)
        quality_score = self.calculate_quality_score(data_records)
        
        # 计算DSI
        dsi = (
            self.dsi_weights['data_volume'] * volume_score +
            self.dsi_weights['time_span'] * time_score +
            self.dsi_weights['time_coverage'] * coverage_score +
            self.dsi_weights['quality_score'] * quality_score
        )
        
        component_scores = {
            'data_volume': volume_score,
            'time_span': time_score,
            'time_coverage': coverage_score,
            'quality_score': quality_score,
            'data_count': data_count,
            'time_span_days': time_span_days
        }
        
        return dsi, component_scores
    
    def determine_sufficiency_level(self, dsi_score: float, user_profile: Optional[Dict] = None) -> Tuple[str, str, float]:
        """确定充分性等级"""
        # 动态调整阈值
        base_threshold = 0.6
        if user_profile:
            stability_score = user_profile.get('stability_score', 0.6)
            if stability_score > 0.8:
                threshold_adjustment = -0.1
            elif stability_score < 0.4:
                threshold_adjustment = 0.2
            else:
                threshold_adjustment = 0.0
            
            adjusted_threshold = max(0.4, min(0.9, base_threshold + threshold_adjustment))
        else:
            adjusted_threshold = base_threshold
        
        # 判断充分性等级
        if dsi_score >= self.sufficiency_thresholds['sufficient']:
            return "充分", "立即建立画像", adjusted_threshold
        elif dsi_score >= adjusted_threshold:
            return "基本充分", "可建立初步画像", adjusted_threshold
        elif dsi_score >= self.sufficiency_thresholds['insufficient']:
            return "不充分", "继续收集数据", adjusted_threshold
        else:
            return "严重不足", "重新评估收集策略", adjusted_threshold
    
    def evaluate_sufficiency(self, data_records: List[DataRecord], user_profile: Optional[Dict] = None) -> SufficiencyResult:
        """评估数据充分性"""
        # 计算DSI
        dsi_score, component_scores = self.calculate_dsi(data_records)
        
        # 确定充分性等级
        level, recommendation, threshold = self.determine_sufficiency_level(dsi_score, user_profile)
        
        # 判断是否可以建立画像
        can_build = level in ["充分", "基本充分"]
        
        return SufficiencyResult(
            dsi_score=dsi_score,
            sufficiency_level=level,
            recommendation=recommendation,
            component_scores=component_scores,
            threshold_used=threshold,
            can_build_profile=can_build
        )

class LayeredMemoryManager:
    """分层记忆管理器"""
    
    def __init__(self):
        self.evaluator = DataSufficiencyEvaluator()
        self.user_baselines = {}  # 用户情绪基线缓存
    
    def calculate_user_baseline(self, user_id: str, data_records: List[DataRecord]) -> float:
        """计算用户情绪基线"""
        if user_id in self.user_baselines:
            return self.user_baselines[user_id]
        
        if not data_records:
            baseline = 5.0  # 默认中性基线
        else:
            # 计算平均情绪分数
            emotion_scores = [r.emotion_score for r in data_records if hasattr(r, 'emotion_score')]
            baseline = statistics.mean(emotion_scores) if emotion_scores else 5.0
        
        self.user_baselines[user_id] = baseline
        return baseline
    
    def calculate_feature_importance(self, record: DataRecord, user_baseline: float, 
                                   recent_emotion: Optional[float] = None) -> float:
        """计算特征重要性得分"""
        # 权重配置
        weights = {
            'emotion_deviation': 2.0,
            'emotion_extremity': 1.5,
            'word_count': 1.0,
            'emotion_change': 1.5,
            'time_relevance': 1.0,
            'emotion_type_diversity': 1.2
        }
        
        # 计算各维度得分
        # 1. 情绪偏离度
        emotion_deviation = abs(record.emotion_score - user_baseline)
        
        # 2. 情绪极值强度
        emotion_extremity = min(abs(record.emotion_score - 0), abs(record.emotion_score - 10)) / 5.0
        
        # 3. 字数权重
        word_weight = min(record.word_count / 50.0, 2.0)
        
        # 4. 情绪变化幅度
        emotion_change = 0.0
        if recent_emotion is not None:
            emotion_change = abs(record.emotion_score - recent_emotion)
        
        # 5. 时间相关性（这里简化为1.0，实际应根据记忆层调整）
        time_relevance = 1.0
        
        # 6. 情绪类型代表性（简化处理）
        emotion_type_diversity = 1.0
        if record.emotion_score > 6.5:  # 正面情绪
            emotion_type_diversity = 1.0
        elif record.emotion_score < 3.5:  # 负面情绪
            emotion_type_diversity = 1.2
        else:  # 中性情绪
            emotion_type_diversity = 0.8
        
        # 计算总得分
        importance_score = (
            weights['emotion_deviation'] * emotion_deviation +
            weights['emotion_extremity'] * emotion_extremity +
            weights['word_count'] * word_weight +
            weights['emotion_change'] * emotion_change +
            weights['time_relevance'] * time_relevance +
            weights['emotion_type_diversity'] * emotion_type_diversity
        )
        
        return importance_score
    
    def allocate_to_layers(self, data_records: List[DataRecord]) -> Dict[str, List[DataRecord]]:
        """将数据分配到各记忆层"""
        if not data_records:
            return {layer: [] for layer in self.evaluator.memory_layers.keys()}
        
        # 按时间排序
        sorted_records = sorted(data_records, key=lambda x: x.timestamp, reverse=True)
        
        # 计算用户基线
        user_id = sorted_records[0].user_id
        user_baseline = self.calculate_user_baseline(user_id, data_records)
        
        # 初始化各层
        layers = {layer: [] for layer in self.evaluator.memory_layers.keys()}
        
        # 当前时间
        now = datetime.now()
        
        # 分配数据到各层
        for record in sorted_records:
            days_ago = (now - record.timestamp).days
            
            # 确定所属层级
            if days_ago <= 7:
                target_layer = 'working'
            elif days_ago <= 28:
                target_layer = 'short_term'
            elif days_ago <= 112:
                target_layer = 'long_term'
            else:
                target_layer = 'core'
            
            # 检查容量限制
            layer_config = self.evaluator.memory_layers[target_layer]
            if len(layers[target_layer]) < layer_config['capacity']:
                layers[target_layer].append(record)
            else:
                # 容量已满，需要选择性保留
                # 计算当前记录的重要性
                importance = self.calculate_feature_importance(record, user_baseline)
                
                # 找到重要性最低的记录
                min_importance = float('inf')
                min_index = -1
                for i, existing_record in enumerate(layers[target_layer]):
                    existing_importance = self.calculate_feature_importance(existing_record, user_baseline)
                    if existing_importance < min_importance:
                        min_importance = existing_importance
                        min_index = i
                
                # 如果当前记录更重要，则替换
                if importance > min_importance:
                    layers[target_layer][min_index] = record
        
        return layers
    
    def get_memory_statistics(self, layers: Dict[str, List[DataRecord]]) -> Dict[str, Any]:
        """获取记忆层统计信息"""
        stats = {}
        
        for layer_name, records in layers.items():
            layer_config = self.evaluator.memory_layers[layer_name]
            
            stats[layer_name] = {
                'count': len(records),
                'capacity': layer_config['capacity'],
                'utilization': len(records) / layer_config['capacity'] if layer_config['capacity'] > 0 else 0,
                'avg_quality': statistics.mean([r.quality_score for r in records]) if records else 0,
                'avg_emotion': statistics.mean([r.emotion_score for r in records]) if records else 0,
                'time_range': {
                    'earliest': min([r.timestamp for r in records]) if records else None,
                    'latest': max([r.timestamp for r in records]) if records else None
                }
            }
        
        return stats

def process_data_sufficiency(params: Dict[str, Any]) -> Dict[str, Any]:
    """处理数据充分性评估请求"""
    try:
        # 解析输入参数
        data_list = params.get('data', [])
        user_profile = params.get('user_profile', {})
        
        if not data_list:
            return {
                'success': False,
                'error': '输入数据为空',
                'result': None
            }
        
        # 转换数据格式
        data_records = []
        for item in data_list:
            try:
                record = DataRecord(
                    user_id=item.get('user_id', 'unknown'),
                    content=item.get('content', ''),
                    emotion_score=float(item.get('emotion_score', 5.0)),
                    quality_score=float(item.get('quality_score', 5.0)),
                    timestamp=datetime.fromisoformat(item.get('timestamp', datetime.now().isoformat())),
                    word_count=int(item.get('word_count', len(item.get('content', '')))),
                    data_id=item.get('data_id', f"record_{len(data_records)}")
                )
                data_records.append(record)
            except (ValueError, TypeError) as e:
                continue  # 跳过格式错误的数据
        
        if not data_records:
            return {
                'success': False,
                'error': '没有有效的数据记录',
                'result': None
            }
        
        # 创建评估器和管理器
        evaluator = DataSufficiencyEvaluator()
        memory_manager = LayeredMemoryManager()
        
        # 评估数据充分性
        sufficiency_result = evaluator.evaluate_sufficiency(data_records, user_profile)
        
        # 分层存储分配
        memory_layers = memory_manager.allocate_to_layers(data_records)
        
        # 获取统计信息
        memory_stats = memory_manager.get_memory_statistics(memory_layers)
        
        # 构建返回结果
        result = {
            'sufficiency_evaluation': {
                'dsi_score': round(sufficiency_result.dsi_score, 4),
                'sufficiency_level': sufficiency_result.sufficiency_level,
                'recommendation': sufficiency_result.recommendation,
                'can_build_profile': sufficiency_result.can_build_profile,
                'threshold_used': round(sufficiency_result.threshold_used, 2),
                'component_scores': {
                    k: round(v, 4) if isinstance(v, float) else v 
                    for k, v in sufficiency_result.component_scores.items()
                }
            },
            'memory_allocation': {
                'layer_distribution': {
                    layer: len(records) for layer, records in memory_layers.items()
                },
                'layer_statistics': memory_stats
            },
            'data_summary': {
                'total_records': len(data_records),
                'avg_quality_score': round(statistics.mean([r.quality_score for r in data_records]), 2),
                'avg_emotion_score': round(statistics.mean([r.emotion_score for r in data_records]), 2),
                'time_span_days': (max([r.timestamp for r in data_records]) - 
                                 min([r.timestamp for r in data_records])).days + 1,
                'users_count': len(set([r.user_id for r in data_records]))
            }
        }
        
        return {
            'success': True,
            'error': None,
            'result': result
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': f'处理过程中发生错误: {str(e)}',
            'result': None
        }

def main(params: Dict[str, Any]) -> Dict[str, Any]:
    """主函数 - 兼容Coze平台调用"""
    return process_data_sufficiency(params)

# 测试代码
if __name__ == "__main__":
    # 准备测试数据
    test_data = {
        'data': [
            {
                'user_id': 'user_001',
                'content': '今天心情不错，工作很顺利',
                'emotion_score': 7.5,
                'quality_score': 8.0,
                'timestamp': (datetime.now() - timedelta(days=1)).isoformat(),
                'word_count': 12
            },
            {
                'user_id': 'user_001',
                'content': '有点累，但是完成了重要任务',
                'emotion_score': 6.0,
                'quality_score': 7.5,
                'timestamp': (datetime.now() - timedelta(days=3)).isoformat(),
                'word_count': 15
            },
            {
                'user_id': 'user_001',
                'content': '今天遇到了一些挫折，心情低落',
                'emotion_score': 3.5,
                'quality_score': 8.5,
                'timestamp': (datetime.now() - timedelta(days=5)).isoformat(),
                'word_count': 16
            },
            {
                'user_id': 'user_001',
                'content': '周末和朋友聚会，非常开心',
                'emotion_score': 9.0,
                'quality_score': 9.0,
                'timestamp': (datetime.now() - timedelta(days=7)).isoformat(),
                'word_count': 14
            },
            {
                'user_id': 'user_001',
                'content': '工作压力很大，需要调整状态',
                'emotion_score': 4.0,
                'quality_score': 7.0,
                'timestamp': (datetime.now() - timedelta(days=10)).isoformat(),
                'word_count': 16
            }
        ],
        'user_profile': {
            'stability_score': 0.7
        }
    }
    
    # 执行测试
    result = main(test_data)
    
    # 输出结果
    print("=== 1.1.3 数据充分性评估体系测试结果 ===")
    print(f"处理状态: {'成功' if result['success'] else '失败'}")
    
    if result['success']:
        data = result['result']
        
        print("\n=== 充分性评估结果 ===")
        sufficiency = data['sufficiency_evaluation']
        print(f"DSI得分: {sufficiency['dsi_score']}")
        print(f"充分性等级: {sufficiency['sufficiency_level']}")
        print(f"建议行动: {sufficiency['recommendation']}")
        print(f"可建立画像: {'是' if sufficiency['can_build_profile'] else '否'}")
        print(f"使用阈值: {sufficiency['threshold_used']}")
        
        print("\n=== 组件得分详情 ===")
        components = sufficiency['component_scores']
        print(f"数据量得分: {components['data_volume']} (数据量: {components['data_count']}条)")
        print(f"时间跨度得分: {components['time_span']} (时间跨度: {components['time_span_days']}天)")
        print(f"时段覆盖得分: {components['time_coverage']}")
        print(f"质量得分: {components['quality_score']}")
        
        print("\n=== 分层存储分配 ===")
        allocation = data['memory_allocation']
        for layer, count in allocation['layer_distribution'].items():
            print(f"{layer}: {count}条数据")
        
        print("\n=== 数据概览 ===")
        summary = data['data_summary']
        print(f"总记录数: {summary['total_records']}")
        print(f"平均质量分: {summary['avg_quality_score']}")
        print(f"平均情绪分: {summary['avg_emotion_score']}")
        print(f"时间跨度: {summary['time_span_days']}天")
        print(f"用户数量: {summary['users_count']}")
        
    else:
        print(f"错误信息: {result['error']}")
    
    print("\n=== 测试完成 ===")