#!/usr/bin/env python3
"""
核心计算逻辑审计：深入检查八维度分数计算和综合适配度计算
"""

import json
import asyncio
from calculation_9_strategy_matching import StrategyMatcher

def audit_core_calculations():
    """审计核心计算逻辑"""
    
    # 测试输入数据
    test_input = {
        "P25": "5.6",
        "P50": "7.6", 
        "P75": "7.8",
        "adaptability_score": "0.83",
        "cem_confidence": "0.553",
        "cem_grade": "基本稳定",
        "cem_value": "-0.226",
        "confidence_level": "0.804",
        "coordination_index": "0.305",
        "crisis_probability": "0.417",
        "ei_confidence": "0.858",
        "ei_value": "0.733",
        "eii_value": "0.46",
        "final_P25": "5.20",
        "final_P50": "8.53",
        "final_P75": "8.60",
        "final_conf_score": "0.13",
        "health_score": "0.69",
        "opportunity_windows": [],
        "risk_level": "中风险",
        "rsi_value": "0.476",
        "stability_trend": "稳定下降",
        "trend_prediction": {
            "medium_term_trend": {
                "confidence": 0.807,
                "direction": "保持稳定",
                "magnitude": 0.08,
                "time_horizon": "3-12个月"
            },
            "short_term_trend": {
                "confidence": 0.949,
                "direction": "保持稳定", 
                "magnitude": 0.1,
                "time_horizon": "1-3个月"
            }
        },
        "user_type": "适应调整型"
    }
    
    print("=== 核心计算逻辑审计 ===")
    
    matcher = StrategyMatcher()
    
    # 1. 审计八维度分数计算
    print("\n1. 八维度分数计算审计:")
    dimension_scores = matcher.calculate_eight_dimension_scores(test_input)
    
    print("   输入参数验证:")
    print(f"   - final_conf_score: {test_input.get('final_conf_score')} (应该使用这个值)")
    print(f"   - confidence_level: {test_input.get('confidence_level')} (备用值)")
    print(f"   - cem_value: {test_input.get('cem_value')}")
    print(f"   - ei_value: {test_input.get('ei_value')}")
    print(f"   - crisis_probability: {test_input.get('crisis_probability')}")
    print(f"   - rsi_value: {test_input.get('rsi_value')}")
    print(f"   - eii_value: {test_input.get('eii_value')}")
    print(f"   - health_score: {test_input.get('health_score')}")
    
    print("\n   计算结果验证:")
    for dimension, score in dimension_scores.items():
        print(f"   - {dimension}: {score:.4f}")
    
    # 手动验证关键计算
    print("\n   手动验证关键计算:")
    
    # 验证用户类型分数
    expected_user_type_score = float(test_input.get('final_conf_score', test_input.get('confidence_level', 0.5)))
    actual_user_type_score = dimension_scores['user_type_score']
    print(f"   - 用户类型分数: 期望={expected_user_type_score}, 实际={actual_user_type_score}, 匹配={abs(expected_user_type_score - actual_user_type_score) < 0.001}")
    
    # 验证情绪动量分数
    cem_value = float(test_input.get('cem_value', 0.0))
    expected_cem_score = max(0.0, min(1.0, (cem_value + 2.0) / 4.0))
    actual_cem_score = dimension_scores['emotional_momentum_score']
    print(f"   - 情绪动量分数: CEM={cem_value}, 期望={expected_cem_score:.4f}, 实际={actual_cem_score:.4f}, 匹配={abs(expected_cem_score - actual_cem_score) < 0.001}")
    
    # 验证情绪强度分数
    ei_value = float(test_input.get('ei_value', 0.0))
    expected_ei_score = max(0.0, min(1.0, ei_value / 2.0))
    actual_ei_score = dimension_scores['emotional_intensity_score']
    print(f"   - 情绪强度分数: EI={ei_value}, 期望={expected_ei_score:.4f}, 实际={actual_ei_score:.4f}, 匹配={abs(expected_ei_score - actual_ei_score) < 0.001}")
    
    # 验证危机评估分数
    crisis_prob = float(test_input.get('crisis_probability', 0.5))
    expected_crisis_score = max(0.0, min(1.0, 1.0 - crisis_prob))
    actual_crisis_score = dimension_scores['crisis_assessment_score']
    print(f"   - 危机评估分数: 危机概率={crisis_prob}, 期望={expected_crisis_score:.4f}, 实际={actual_crisis_score:.4f}, 匹配={abs(expected_crisis_score - actual_crisis_score) < 0.001}")
    
    # 2. 审计综合适配度计算
    print("\n2. 综合适配度计算审计:")
    adaptability_score = matcher.calculate_comprehensive_adaptability(test_input, dimension_scores)
    
    print("   权重配置:")
    for dimension, weight in matcher.dimension_weights.items():
        print(f"   - {dimension}: {weight}")
    
    print("\n   加权计算验证:")
    manual_weighted_score = 0.0
    score_mapping = {
        'user_type': dimension_scores['user_type_score'],
        'emotional_momentum': dimension_scores['emotional_momentum_score'],
        'emotional_intensity': dimension_scores['emotional_intensity_score'],
        'relationship_stability': dimension_scores['relationship_stability_score'],
        'emotional_inertia': dimension_scores['emotional_inertia_score'],
        'crisis_assessment': dimension_scores['crisis_assessment_score'],
        'health_assessment': dimension_scores['health_assessment_score'],
        'trend_prediction': dimension_scores['trend_prediction_score']
    }
    
    for dimension, weight in matcher.dimension_weights.items():
        score = score_mapping[dimension]
        contribution = score * weight
        manual_weighted_score += contribution
        print(f"   - {dimension}: {score:.4f} × {weight} = {contribution:.4f}")
    
    manual_final_score = min(1.0, max(0.0, manual_weighted_score))
    print(f"\n   手动计算结果: {manual_final_score:.4f}")
    print(f"   系统计算结果: {adaptability_score:.4f}")
    print(f"   计算匹配: {abs(manual_final_score - adaptability_score) < 0.001}")
    
    # 3. 检查潜在问题
    print("\n3. 潜在问题检查:")
    
    issues = []
    
    # 检查分数范围
    for dimension, score in dimension_scores.items():
        if score < 0 or score > 1:
            issues.append(f"维度 {dimension} 分数超出范围 [0,1]: {score}")
    
    # 检查权重总和
    total_weight = sum(matcher.dimension_weights.values())
    if abs(total_weight - 1.0) > 0.001:
        issues.append(f"权重总和不等于1.0: {total_weight}")
    
    # 检查参数使用
    if test_input.get('final_conf_score') and test_input.get('confidence_level'):
        if float(test_input['final_conf_score']) != float(test_input['confidence_level']):
            issues.append(f"final_conf_score ({test_input['final_conf_score']}) 与 confidence_level ({test_input['confidence_level']}) 不一致")
    
    # 检查趋势预测计算
    trend_score = dimension_scores['trend_prediction_score']
    if trend_score == 0.5:  # 默认值，可能表示计算有问题
        issues.append("趋势预测分数为默认值0.5，可能存在计算问题")
    
    if issues:
        print("   发现的问题:")
        for issue in issues:
            print(f"   ❌ {issue}")
    else:
        print("   ✅ 未发现明显问题")
    
    return {
        'dimension_scores': dimension_scores,
        'adaptability_score': adaptability_score,
        'issues': issues
    }

if __name__ == "__main__":
    audit_core_calculations()
