def main(args):
    """数据质量验证系统主函数 - 符合1.1.2功能要求"""
    try:
        # 处理输入数据 - 支持多种数据格式
        input_data = {}
        
        # 调试：记录原始输入
        original_args = args
        
        # 处理Args对象 - 检查所有可能的属性
        if hasattr(args, 'params'):
            # 检查params的不同格式
            if hasattr(args.params, 'get') and 'input' in args.params:
                input_data = args.params.get('input', {})
            elif hasattr(args.params, 'get'):
                # params本身可能就是数据
                input_data = args.params
            else:
                # params可能是其他类型
                input_data = args.params if args.params else {}
        elif hasattr(args, 'input'):
            # 直接有input属性
            input_data = args.input
        elif hasattr(args, '__dict__'):
            # 尝试获取对象的所有属性
            args_dict = vars(args)
            # 查找可能包含数据的属性
            for attr_name in ['input', 'data', 'params', 'args']:
                if attr_name in args_dict:
                    input_data = args_dict[attr_name]
                    break
            else:
                # 如果没找到，使用整个字典
                input_data = args_dict
        elif isinstance(args, dict):
            input_data = args
        elif isinstance(args, str):
            # 如果是JSON字符串，尝试解析
            import json
            try:
                input_data = json.loads(args)
            except:
                input_data = {}
        elif args is None:
            # 如果args为None，可能需要从其他地方获取数据
            input_data = {}
        else:
            # 其他情况，尝试直接使用
            input_data = args if args else {}
        
        # 提取数据 - 根据文档设计改进缺失值处理
        content = ""
        emo_value = None  # 改为None，符合文档中的数据缺失处理策略
        user_id = "unknown"
        raw_emo_value = None  # 初始化变量
        
        # 支持多种数据格式
        if 'immediate_memory' in input_data and input_data['immediate_memory']:
            # 原有的immediate_memory格式
            latest = input_data['immediate_memory'][-1]
            content = latest.get('conversation', '')
            raw_emo_value = latest.get('emo_value')
            user_id = latest.get('uid', 'unknown')

        elif 'conversation' in input_data or 'content' in input_data:
            # 直接的对话数据格式
            content = input_data.get('conversation', input_data.get('content', ''))
            raw_emo_value = input_data.get('emo_value')
            user_id = input_data.get('uid', input_data.get('user_id', 'unknown'))
        elif isinstance(input_data, list) and len(input_data) > 0:
            # 列表格式，取最后一个
            latest = input_data[-1]
            if isinstance(latest, dict):
                content = latest.get('conversation', latest.get('content', ''))
                raw_emo_value = latest.get('emo_value')
                user_id = latest.get('uid', latest.get('user_id', 'unknown'))
        
        # 处理emo_value转换
        if raw_emo_value is not None and str(raw_emo_value).strip() != '':
            try:
                # 处理字符串和数字类型的emo_value
                emo_value = float(str(raw_emo_value).strip())
                # 确保在合理范围内
                if emo_value < 0 or emo_value > 10:
                    emo_value = None
            except (ValueError, TypeError):
                emo_value = None  # 转换失败时设为None
        
        # 数据质量评分算法 - 处理缺失值版本
        def calculate_quality_score(content, emo_value, completeness_score):
            """计算质量分数 - 处理缺失值版本"""
            # 内容质量评分 (0-2分)
            content_length = len(content.strip())
            if content_length == 0:
                content_score = 0
            elif content_length < 10:
                content_score = 0.5
            elif content_length < 50:
                content_score = 1.0
            elif content_length < 200:
                content_score = 1.5
            else:
                content_score = 2.0
            
            # 情感值质量评分 (0-1.5分) - 处理None值
            if emo_value is None:
                emotion_score = 0.75  # 缺失值给予中等分数，避免过度惩罚
            elif emo_value < 1 or emo_value > 10:
                emotion_score = 0  # 异常情感值
            elif 1 <= emo_value <= 2 or 9 <= emo_value <= 10:
                emotion_score = 0.5  # 极端情感值
            elif 2 < emo_value < 4 or 7 < emo_value < 9:
                emotion_score = 1.0  # 较强情感值
            else:  # 4-7之间
                emotion_score = 1.5  # 中性情感值
            
            # 最终质量分数 = 内容分数 + 情感分数 + 完整性分数的一部分
            quality_score = content_score + emotion_score + (completeness_score * 0.5)
            
            return round(quality_score, 2)
        
        # 质量等级判定
        def determine_quality_grade(score):
            if score >= 8.0:
                return "A级（优质）", "直接使用", True
            elif score >= 6.0:
                return "B级（良好）", "正常使用", True
            elif score >= 4.0:
                return "C级（可用）", "谨慎使用", False
            else:
                return "D级（异常）", "需要清洗或丢弃", False
        
        # 异常检测 (基于Z-score)
        def detect_anomaly(content, emo_value):
            anomaly_score = 0.0
            anomaly_level = "normal"
            suggestion = "正常使用"
            
            # 情感值异常检测
            if emo_value is not None:
                if emo_value < 1 or emo_value > 9:
                    anomaly_score += 0.3
                if emo_value < 0 or emo_value > 10:
                    anomaly_score += 0.5
            
            # 内容异常检测
            if content:
                content_len = len(content)
                if content_len < 3:
                    anomaly_score += 0.4
                elif content_len > 2000:
                    anomaly_score += 0.3
            
            # 异常等级判定
            if anomaly_score >= 0.8:
                anomaly_level = "severe"
                suggestion = "隔离处理"
            elif anomaly_score >= 0.6:
                anomaly_level = "moderate"
                suggestion = "标记审核"
            elif anomaly_score >= 0.3:
                anomaly_level = "mild"
                suggestion = "降权处理"
            
            return anomaly_level, anomaly_score, suggestion
        
        # 完整性评分 - 处理缺失值版本
        def calculate_completeness_score(content, emo_value):
            """计算完整性评分 - 处理None值"""
            score = 0.0
            
            # 内容完整性 (0-5分)
            if content and len(content.strip()) > 0:
                content_len = len(content.strip())
                if content_len >= 100:
                    score += 5.0
                elif content_len >= 50:
                    score += 4.0
                elif content_len >= 20:
                    score += 3.0
                elif content_len >= 10:
                    score += 2.0
                else:
                    score += 1.0
            
            # 情感值完整性 (0-3分) - 处理None值
            if emo_value is None:
                score += 1.5  # 缺失值给予部分分数
            elif 0 <= emo_value <= 10:
                score += 3.0  # 正常范围
            else:
                score += 1.0  # 异常值
            
            return min(8.0, score)
        
        # 一致性评分 - 处理缺失值版本
        def calculate_consistency_score(content, emo_value):
            """计算一致性评分 - 处理None值"""
            score = 0.0
            
            # 内容一致性检查 (0-4分)
            if content and len(content.strip()) > 0:
                # 检查内容质量和一致性
                if any(char.isalnum() for char in content):
                    score += 1.5
                if len(content.split()) >= 3:
                    score += 1.0
                if not any(word in content.lower() for word in ['undefined', 'null', 'error']):
                    score += 1.0
                if len(set(content.split())) / max(len(content.split()), 1) > 0.7:  # 词汇多样性
                    score += 0.5
            
            # 情感值一致性检查 (0-4分) - 处理None值
            if emo_value is None:
                score += 2.0  # 缺失值给予中等分数
            elif 1 <= emo_value <= 10:
                score += 4.0  # 合理范围
            elif 0 <= emo_value <= 11:
                score += 2.0  # 可接受范围
            else:
                score += 0.5  # 异常范围
            
            return min(8.0, score)
        
        # 计算各项分数
        completeness_score = calculate_completeness_score(content, emo_value)
        consistency_score = calculate_consistency_score(content, emo_value)
        
        # 执行质量验证
        quality_score = calculate_quality_score(content, emo_value, completeness_score)
        quality_grade, processing_strategy, should_store = determine_quality_grade(quality_score)
        anomaly_level, anomaly_score, anomaly_suggestion = detect_anomaly(content, emo_value)
        
        # 存储决策 (综合质量等级和异常检测结果)
        final_should_store = should_store and anomaly_level in ["normal", "mild"]
        
        # 权重调整（基于异常检测结果）
        adjusted_weight = 1.0
        if anomaly_level == "mild":
            adjusted_weight = 0.8
        elif anomaly_level == "moderate":
            adjusted_weight = 0.6
        elif anomaly_level == "severe":
            adjusted_weight = 0.3
        
        # 返回期望的输出结构（包含调试信息）
        return {
            "quality_score": round(quality_score, 2),
            "completeness_score": round(completeness_score, 2),
            "consistency_score": round(consistency_score, 2),
            "quality_grade": quality_grade,
            "handling_strategy": processing_strategy,
            "adjusted_weight": round(adjusted_weight, 2),
            "debug_info": {
                "content": content,
                "emo_value": emo_value,
                "content_length": len(content.strip()) if content else 0,
                "raw_emo_value": raw_emo_value,
                "original_args_type": str(type(original_args)),
                "original_args_sample": str(original_args)[:200] if original_args else "None",
                "original_args_attrs": list(vars(original_args).keys()) if hasattr(original_args, '__dict__') else "no_dict",
                "params_type": str(type(original_args.params)) if hasattr(original_args, 'params') else "no_params",
                "params_sample": str(original_args.params)[:300] if hasattr(original_args, 'params') else "no_params",
                "input_data_type": str(type(input_data)),
                "input_data_keys": list(input_data.keys()) if isinstance(input_data, dict) else "not_dict",
                "input_data_sample": str(input_data)[:200] if input_data else "empty"
            }
        }
        
    except Exception as e:
        return {
            "quality_score": 0.0,
            "completeness_score": 0.0,
            "consistency_score": 0.0,
            "quality_grade": "D级（异常）",
            "handling_strategy": "错误处理",
            "adjusted_weight": 0.0,
            "error_info": {
                "error_type": str(type(e).__name__),
                "error_message": str(e),
                "input_data_keys": list(args.keys()) if isinstance(args, dict) else "not_dict"
            }
        }