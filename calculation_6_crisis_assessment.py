"""
计算6：危机分数计算模块
基于多模块输出的关系危机识别和风险评估体系
"""

import json
import time
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import uuid

# 用户类型危机倾向系数配置
USER_TYPE_CRISIS_COEFFICIENTS = {
    "积极稳定型": 0.8,
    "沉稳内敛型": 0.9,
    "情绪敏感型": 1.2,
    "消极波动型": 1.3,
    "适应调整型": 1.1
}

# 用户类型动态阈值配置
USER_TYPE_THRESHOLDS = {
    "积极稳定型": {"rsi": 0.35, "coordination": 0.35, "eii": 0.25},
    "沉稳内敛型": {"rsi": 0.4, "coordination": 0.4, "eii": 0.3},
    "情绪敏感型": {"rsi": 0.45, "coordination": 0.45, "eii": 0.35},
    "消极波动型": {"rsi": 0.5, "coordination": 0.5, "eii": 0.4},
    "适应调整型": {"rsi": 0.42, "coordination": 0.42, "eii": 0.32}
}

# 风险等级定义
RISK_LEVELS = [
    {"min": 0.8, "max": 1.0, "level": "极高风险", "warning": "红色预警", "urgency": "紧急干预"},
    {"min": 0.6, "max": 0.8, "level": "高风险", "warning": "橙色预警", "urgency": "重点关注"},
    {"min": 0.4, "max": 0.6, "level": "中风险", "warning": "黄色预警", "urgency": "积极干预"},
    {"min": 0.2, "max": 0.4, "level": "低风险", "warning": "蓝色预警", "urgency": "预防性关注"},
    {"min": 0.0, "max": 0.2, "level": "安全", "warning": "无预警", "urgency": "常规监控"}
]

class CrisisAssessment:
    """危机评估核心类"""
    
    def __init__(self):
        self.start_time = time.time()
        self.calculation_id = f"crisis_calc_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        
    def safe_float_convert(self, value: Any, default: float = 0.0) -> float:
        """安全的浮点数转换"""
        try:
            if isinstance(value, str):
                if value.lower() in ['', 'null', 'none', 'nan']:
                    return default
                return float(value)
            elif isinstance(value, (int, float)):
                return float(value)
            else:
                return default
        except (ValueError, TypeError):
            return default
    
    def calculate_comprehensive_confidence(self, params: Dict) -> float:
        """计算综合置信度"""
        # confidence_level 对应 RSI相关的置信度
        rsi_conf = self.safe_float_convert(params.get('confidence_level', 0.0))
        # eii_confidence 从计算5的 l3_output_confidence 获取
        eii_conf = self.safe_float_convert(params.get('eii_confidence', 0.0))

        # 简化的置信度计算（由于输入限制，部分参数可能缺失）
        if rsi_conf > 0 and eii_conf > 0:
            return rsi_conf * 0.6 + eii_conf * 0.4
        elif rsi_conf > 0:
            return rsi_conf * 0.8
        elif eii_conf > 0:
            return eii_conf * 0.8
        else:
            return 0.3  # 默认低置信度
    
    def determine_assessment_mode(self, confidence: float) -> str:
        """确定评估模式"""
        if confidence >= 0.5:
            return "标准评估"
        elif confidence >= 0.3:
            return "简化评估"
        else:
            return "默认评估"
    
    def get_dynamic_thresholds(self, user_type: str, assessment_mode: str) -> Dict:
        """获取动态阈值"""
        base_thresholds = USER_TYPE_THRESHOLDS.get(user_type, USER_TYPE_THRESHOLDS["沉稳内敛型"])
        
        # 根据评估模式调整阈值
        if assessment_mode == "简化评估":
            return {k: v * 0.8 for k, v in base_thresholds.items()}  # 阈值降低20%
        elif assessment_mode == "默认评估":
            return {k: v * 0.6 for k, v in base_thresholds.items()}  # 阈值降低40%
        else:
            return base_thresholds
    
    def check_crisis_triggers(self, params: Dict, thresholds: Dict) -> List[str]:
        """检查危机触发条件"""
        triggers = []
        
        rsi_value = self.safe_float_convert(params.get('rsi_value', 1.0))
        coordination_index = self.safe_float_convert(params.get('coordination_index', 1.0))
        eii_value = self.safe_float_convert(params.get('eii_value', 1.0))
        
        if rsi_value < thresholds['rsi']:
            triggers.append("RSI稳定性过低")
        
        if coordination_index < thresholds['coordination']:
            triggers.append("参数协调性严重失调")
        
        if eii_value < thresholds['eii']:
            triggers.append("情绪惯性异常低")
        
        stability_trend = params.get('stability_trend', '')
        if stability_trend in ["稳定下降", "急剧下降"]:
            triggers.append("稳定性持续下降")
        
        # 检查多维度稳定性异常
        s_stability = self.safe_float_convert(params.get('s_stability_factor', 1.0))
        m_stability = self.safe_float_convert(params.get('m_stability_factor', 1.0))
        t_stability = self.safe_float_convert(params.get('t_stability_factor', 1.0))
        
        low_factors = sum(1 for factor in [s_stability, m_stability, t_stability] if factor < 0.4)
        if low_factors >= 2:
            triggers.append("多维度稳定性异常")
        
        return triggers
    
    def calculate_risk_factors(self, params: Dict, user_type: str, assessment_mode: str) -> Dict:
        """计算风险因子"""
        # 获取用户类型系数
        type_coefficient = USER_TYPE_CRISIS_COEFFICIENTS.get(user_type, 1.0)
        
        # S参数风险因子
        s_stability = self.safe_float_convert(params.get('s_stability_factor', 1.0))
        s_risk = max(0, 0.5 - s_stability) * 2 * type_coefficient
        s_risk = min(1.0, s_risk)
        
        # M参数风险因子
        m_stability = self.safe_float_convert(params.get('m_stability_factor', 1.0))
        m_risk = max(0, 0.5 - m_stability) * 2 * type_coefficient
        m_risk = min(1.0, m_risk)
        
        # T参数风险因子
        t_stability = self.safe_float_convert(params.get('t_stability_factor', 1.0))
        t_risk = max(0, 0.5 - t_stability) * 2 * type_coefficient
        t_risk = min(1.0, t_risk)
        
        # 如果是简化评估，只计算核心风险因子
        if assessment_mode == "简化评估":
            # 重点关注S和RSI
            rsi_value = self.safe_float_convert(params.get('rsi_value', 1.0))
            rsi_risk = max(0, 0.5 - rsi_value) * 2
            s_risk = max(s_risk, rsi_risk)
        
        return {
            "s_risk_factor": s_risk,
            "m_risk_factor": m_risk,
            "t_risk_factor": t_risk
        }
    
    def calculate_crisis_probability(self, risk_factors: Dict, params: Dict, user_type: str) -> float:
        """计算危机概率"""
        # 步骤1：基础危机概率
        base_crisis_prob = (
            risk_factors['s_risk_factor'] * 0.4 +
            risk_factors['m_risk_factor'] * 0.35 +
            risk_factors['t_risk_factor'] * 0.25
        )
        
        # 步骤2：RSI稳定性修正
        rsi_value = self.safe_float_convert(params.get('rsi_value', 1.0))
        if rsi_value < 0.2:
            rsi_correction = 1.5
        elif rsi_value < 0.4:
            rsi_correction = 1.2
        else:
            rsi_correction = 1.0
        
        # 步骤3：EII惯性修正
        eii_value = self.safe_float_convert(params.get('eii_value', 1.0))
        if eii_value < 0.3:
            eii_correction = 1.3
        elif eii_value > 0.8:
            eii_correction = 0.8
        else:
            eii_correction = 1.0
        
        # 步骤4：用户类型校正
        type_correction = USER_TYPE_CRISIS_COEFFICIENTS.get(user_type, 1.0)
        
        # 步骤5：趋势修正
        stability_trend = params.get('stability_trend', '')
        if stability_trend == "急剧下降":
            trend_correction = 1.5
        elif stability_trend == "稳定下降":
            trend_correction = 1.3
        else:
            trend_correction = 1.0
        
        # 最终危机概率
        crisis_probability = (
            base_crisis_prob * rsi_correction * eii_correction * 
            type_correction * trend_correction
        )
        
        return min(1.0, max(0.0, crisis_probability))
    
    def determine_risk_level(self, crisis_probability: float) -> Dict:
        """确定风险等级"""
        for level_info in RISK_LEVELS:
            if level_info['min'] <= crisis_probability < level_info['max']:
                return level_info
        
        # 默认返回最高风险等级
        return RISK_LEVELS[0]
    
    def generate_crisis_context(self, risk_level_info: Dict, triggers: List[str], user_type: str) -> Dict:
        """生成危机上下文信息（供策略匹配树使用）"""
        # 计算6只提供风险状态描述，不提供具体策略
        crisis_context = {
            "urgency_level": self._map_urgency_level(risk_level_info['urgency']),
            "risk_characteristics": self._analyze_risk_characteristics(triggers),
            "user_vulnerability": self._assess_user_vulnerability(user_type),
            "intervention_priority": self._determine_intervention_priority(risk_level_info['level'])
        }

        return crisis_context

    def _map_urgency_level(self, urgency: str) -> int:
        """映射紧急程度为数值等级"""
        urgency_mapping = {
            "紧急干预": 5,
            "重点关注": 4,
            "积极干预": 3,
            "预防性关注": 2,
            "常规监控": 1
        }
        return urgency_mapping.get(urgency, 1)

    def _analyze_risk_characteristics(self, triggers: List[str]) -> Dict:
        """分析风险特征（不提供解决方案）"""
        return {
            "primary_triggers": triggers[:3],  # 主要触发因素
            "trigger_count": len(triggers),
            "complexity_level": "复杂" if len(triggers) > 3 else "简单"
        }

    def _assess_user_vulnerability(self, user_type: str) -> Dict:
        """评估用户脆弱性（不提供应对策略）"""
        vulnerability_mapping = {
            "积极稳定型": {"level": "低", "score": 0.2},
            "沉稳内敛型": {"level": "中低", "score": 0.3},
            "情绪敏感型": {"level": "高", "score": 0.8},
            "消极波动型": {"level": "极高", "score": 0.9},
            "适应调整型": {"level": "中", "score": 0.5}
        }
        return vulnerability_mapping.get(user_type, {"level": "中", "score": 0.5})

    def _determine_intervention_priority(self, risk_level: str) -> str:
        """确定干预优先级（不指定具体干预方式）"""
        priority_mapping = {
            "极高风险": "P0-立即响应",
            "高风险": "P1-优先处理",
            "中风险": "P2-及时关注",
            "低风险": "P3-定期监控",
            "安全": "P4-常规维护"
        }
        return priority_mapping.get(risk_level, "P3-定期监控")

    def calculate_confidence_breakdown(self, params: Dict, validation_passed: bool) -> Dict:
        """计算置信度分解"""
        # L1-输入质量置信度
        # confidence_level 对应 RSI相关的置信度
        rsi_conf = self.safe_float_convert(params.get('confidence_level', 0.0))
        # eii_confidence 从计算5的 l3_output_confidence 获取
        eii_conf = self.safe_float_convert(params.get('eii_confidence', 0.0))

        l1_confidence = 0.0
        if rsi_conf > 0 and eii_conf > 0:
            l1_confidence = rsi_conf * 0.6 + eii_conf * 0.4
        elif rsi_conf > 0:
            l1_confidence = rsi_conf * 0.8
        elif eii_conf > 0:
            l1_confidence = eii_conf * 0.8
        else:
            l1_confidence = 0.3

        # L2-计算稳定性置信度
        data_sufficiency = min(1.0, 1.0)  # 假设数据充分性为1.0
        calculation_stability = 0.9 if validation_passed else 0.6
        l2_confidence = data_sufficiency * calculation_stability

        # L3-输出置信度
        l3_confidence = ((l1_confidence ** 0.6) * (l2_confidence ** 0.4)) ** 0.95

        return {
            "l1_input_quality": round(l1_confidence, 3),
            "l2_calculation_stability": round(l2_confidence, 3),
            "l3_output_confidence": round(l3_confidence, 3)
        }

    def validate_results(self, crisis_probability: float, risk_factors: Dict, risk_level_info: Dict) -> Dict:
        """验证计算结果"""
        validation_result = {
            "probability_validation": True,
            "factor_consistency": True,
            "warning_level_match": True,
            "anomaly_detected": False,
            "performance_check": True
        }

        # 概率范围验证
        if not (0.0 <= crisis_probability <= 1.0):
            validation_result["probability_validation"] = False

        # 风险因子一致性验证
        for factor_name, factor_value in risk_factors.items():
            if not (0.0 <= factor_value <= 1.0):
                validation_result["factor_consistency"] = False
                break

        # 性能检查
        processing_time = (time.time() - self.start_time) * 1000  # 转换为毫秒
        if processing_time > 60:  # 超过60ms
            validation_result["performance_check"] = False

        return validation_result

    def format_output(self, crisis_probability: float, risk_factors: Dict, risk_level_info: Dict,
                     triggers: List[str], crisis_context: Dict, confidence_breakdown: Dict,
                     validation_result: Dict, params: Dict, assessment_mode: str) -> Dict:
        """格式化输出结果"""
        processing_time = round((time.time() - self.start_time) * 1000, 2)

        # 识别主要风险源
        primary_risk_sources = []
        if risk_factors['s_risk_factor'] > 0.5:
            primary_risk_sources.append("情绪稳定性风险")
        if risk_factors['m_risk_factor'] > 0.5:
            primary_risk_sources.append("投入度风险")
        if risk_factors['t_risk_factor'] > 0.5:
            primary_risk_sources.append("时间模式风险")

        # 综合风险评分
        comprehensive_risk = (
            risk_factors['s_risk_factor'] * 0.4 +
            risk_factors['m_risk_factor'] * 0.35 +
            risk_factors['t_risk_factor'] * 0.25
        )

        # 预期恶化时间
        if crisis_probability >= 0.8:
            expected_escalation = "24小时内可能恶化"
        elif crisis_probability >= 0.6:
            expected_escalation = "48小时内可能恶化"
        elif crisis_probability >= 0.4:
            expected_escalation = "一周内需要关注"
        else:
            expected_escalation = "短期内风险可控"

        return {
            "calculation_id": self.calculation_id,
            "calculation_type": "crisis_assessment",
            "version": "6.2.0",
            "timestamp": datetime.now().isoformat(),

            "crisis_assessment": {
                "crisis_probability": round(crisis_probability, 3),
                "risk_level": risk_level_info['level'],
                "warning_level": risk_level_info['warning'],
                "crisis_trend": "持续恶化" if crisis_probability > 0.6 else "相对稳定",
                "intervention_urgency": risk_level_info['urgency']
            },

            "risk_analysis": {
                "s_risk_factor": round(risk_factors['s_risk_factor'], 3),
                "m_risk_factor": round(risk_factors['m_risk_factor'], 3),
                "t_risk_factor": round(risk_factors['t_risk_factor'], 3),
                "comprehensive_risk": round(comprehensive_risk, 3),
                "primary_risk_sources": primary_risk_sources,
                "risk_trend": "上升" if crisis_probability > 0.5 else "稳定"
            },

            "warning_details": {
                "warning_triggers": triggers,
                "intervention_timing": "立即干预" if crisis_probability > 0.6 else "适时关注",
                "expected_escalation": expected_escalation,
                "critical_factors": ["情绪稳定性", "关系投入度", "时间管理"]
            },

            "crisis_context": crisis_context,

            "confidence_breakdown": confidence_breakdown,

            "validation_result": validation_result,

            "metadata": {
                "assessment_basis": "RSI稳定性分析+EII惯性评估",
                "assessment_mode": assessment_mode,
                "user_type": params.get('user_type', '未知'),
                "processing_time_ms": processing_time,
                "unique_output": "危机风险评估"
            }
        }


async def main(args) -> Dict:
    """主函数：计算6危机分数计算"""
    try:
        # 获取输入参数
        params = args.params

        # 创建危机评估实例
        crisis_assessment = CrisisAssessment()

        # 1. 计算综合置信度
        comprehensive_confidence = crisis_assessment.calculate_comprehensive_confidence(params)

        # 2. 确定评估模式
        assessment_mode = crisis_assessment.determine_assessment_mode(comprehensive_confidence)

        # 3. 获取用户类型和动态阈值
        user_type = params.get('user_type', '沉稳内敛型')
        dynamic_thresholds = crisis_assessment.get_dynamic_thresholds(user_type, assessment_mode)

        # 4. 检查危机触发条件
        crisis_triggers = crisis_assessment.check_crisis_triggers(params, dynamic_thresholds)

        # 5. 计算风险因子
        risk_factors = crisis_assessment.calculate_risk_factors(params, user_type, assessment_mode)

        # 6. 计算危机概率
        crisis_probability = crisis_assessment.calculate_crisis_probability(risk_factors, params, user_type)

        # 7. 确定风险等级
        risk_level_info = crisis_assessment.determine_risk_level(crisis_probability)

        # 8. 生成危机上下文信息（供策略匹配树使用）
        crisis_context = crisis_assessment.generate_crisis_context(
            risk_level_info, crisis_triggers, user_type
        )

        # 9. 验证结果
        validation_result = crisis_assessment.validate_results(crisis_probability, risk_factors, risk_level_info)

        # 10. 计算置信度分解
        confidence_breakdown = crisis_assessment.calculate_confidence_breakdown(
            params, validation_result.get('probability_validation', True)
        )

        # 11. 格式化输出
        output = crisis_assessment.format_output(
            crisis_probability, risk_factors, risk_level_info, crisis_triggers,
            crisis_context, confidence_breakdown, validation_result, params, assessment_mode
        )

        return output

    except Exception as e:
        # 错误处理：返回默认的安全评估结果
        return {
            "calculation_id": f"crisis_calc_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "calculation_type": "crisis_assessment",
            "version": "6.2.0",
            "timestamp": datetime.now().isoformat(),
            "crisis_assessment": {
                "crisis_probability": 0.3,
                "risk_level": "中风险",
                "warning_level": "黄色预警",
                "crisis_trend": "数据不足",
                "intervention_urgency": "预防性关注"
            },
            "error_info": {
                "error_occurred": True,
                "error_message": str(e),
                "fallback_mode": "默认安全评估"
            },
            "metadata": {
                "assessment_mode": "错误降级模式",
                "processing_time_ms": 0,
                "unique_output": "危机风险评估"
            }
        }
