# Coze平台 - 1.1.2节数据质量验证体系（最终版本）
# 基于《方案分析报告2025.6.23-3_修改版.md》1.1.2节需求开发
# 适配Coze平台Python代码规范

from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import json
import re
import time

# Coze平台标准类型定义
class Args:
    def __init__(self, params: Dict[str, Any]):
        self.params = params

Output = Dict[str, Any]

@dataclass
class EmotionData:
    """情绪数据结构 - 符合1.1.2节数据模型定义"""
    bstudio_create_time: str
    conversation: str
    emo_value: str
    number: str
    
    def __post_init__(self):
        # 标准化字段映射
        self.timestamp = self.bstudio_create_time
        self.expression_text = self.conversation
        self.expression_length = int(self.number) if self.number.isdigit() else len(self.conversation)
        
        # 情绪强度转换
        try:
            self.emotion_intensity = float(self.emo_value)
        except (ValueError, TypeError):
            self.emotion_intensity = 5.0  # 默认中性值
        
        # 情绪类型映射（基于强度区间）
        if self.emotion_intensity >= 7.0:
            self.emotion_type = 'joy'
        elif self.emotion_intensity >= 5.5:
            self.emotion_type = 'satisfaction'
        elif self.emotion_intensity >= 4.5:
            self.emotion_type = 'neutral'
        elif self.emotion_intensity >= 3.0:
            self.emotion_type = 'dissatisfaction'
        else:
            self.emotion_type = 'sadness'
        
        # 表达复杂度计算
        if self.expression_text:
            unique_chars = len(set(self.expression_text))
            text_length = len(self.expression_text)
            self.expression_complexity = min(10.0, (unique_chars / max(1, text_length)) * 20 + text_length * 0.1)
        else:
            self.expression_complexity = 0.0
        
        # 时间上下文提取
        self.time_context = self._extract_time_context()
        
        # 生成会话和用户标识
        self.session_id = f"session_{int(time.time())}_{hash(self.conversation) % 10000}"
        self.user_id = f"user_{int(time.time()) // 86400}"  # 按天分组
    
    def _extract_time_context(self) -> str:
        """从时间戳提取时间上下文"""
        try:
            # 简单的时间段判断
            if any(h in self.timestamp for h in ['06:', '07:', '08:', '09:', '10:', '11:']):
                return 'morning'
            elif any(h in self.timestamp for h in ['12:', '13:', '14:', '15:', '16:', '17:']):
                return 'afternoon'
            elif any(h in self.timestamp for h in ['18:', '19:', '20:', '21:']):
                return 'evening'
            else:
                return 'night'
        except:
            return 'unknown'

class QualityLevel(Enum):
    """质量等级枚举 - 符合1.1.2节分级标准"""
    A = "A"  # 8.0-10.0分，优先选取
    B = "B"  # 6.0-7.9分，正常选取
    C = "C"  # 4.0-5.9分，容量不足时选取
    D = "D"  # 0.0-3.9分，直接舍弃

@dataclass
class QualityScore:
    """质量评分结果"""
    dqs_score: float  # DQS总分
    quality_level: str  # 质量等级
    weight_coefficient: float  # 权重系数
    completeness_score: float  # 完整性分数
    consistency_score: float  # 一致性分数
    detailed_scores: Dict[str, Any]  # 详细评分
    issues: List[str]  # 发现的问题

class DataQualityValidator:
    """数据质量验证器 - 实现1.1.2节二维质量评分模型"""
    
    def __init__(self):
        # 质量阈值定义（符合1.1.2节标准）
        self.quality_thresholds = {
            'A': (8.0, 10.0, 1.0),   # (最小分, 最大分, 权重系数)
            'B': (6.0, 7.9, 0.8),
            'C': (4.0, 5.9, 0.5),
            'D': (0.0, 3.9, 0.1)
        }
        
        # 权重配置
        self.completeness_weight = 0.6  # 完整性权重
        self.consistency_weight = 0.4   # 一致性权重
        
        # 冷启动默认基线（当用户数据量<10时使用）
        self.cold_start_baseline = {
            'P25': 4.0,  # 25%分位数
            'P50': 5.0,  # 50%分位数（中位数）
            'P75': 6.0   # 75%分位数
        }
    
    def validate_data_quality(self, emotion_data: EmotionData, baseline: Dict[str, Any] = None) -> QualityScore:
        """验证数据质量 - 核心算法实现
        
        Args:
            emotion_data: 情绪数据对象
            baseline: 个体基线数据（可选）
            
        Returns:
            QualityScore: 质量评分结果
        """
        if baseline is None:
            baseline = {}
        
        # 1. 完整性评估（60%权重）
        completeness_score = self._assess_completeness(emotion_data)
        
        # 2. 一致性评估（40%权重）
        consistency_score = self._assess_consistency(emotion_data, baseline)
        
        # 3. 计算DQS总分
        dqs_score = (completeness_score * self.completeness_weight + 
                    consistency_score * self.consistency_weight)
        
        # 4. 确定质量等级和权重系数
        quality_level, weight_coefficient = self._determine_quality_level(dqs_score)
        
        # 5. 收集详细评分信息
        detailed_scores = {
            'field_completeness': self._check_field_completeness(emotion_data),
            'timestamp_validity': self._is_valid_timestamp(emotion_data.timestamp),
            'content_richness': min(10.0, len(emotion_data.expression_text) * 0.5),
            'text_pattern_check': not self._detect_text_patterns(emotion_data.expression_text),
            'baseline_deviation': self._calculate_baseline_deviation(emotion_data, baseline)
        }
        
        # 6. 收集发现的问题
        issues = []
        if completeness_score < 8.0:
            issues.append('数据完整性不足')
        if consistency_score < 6.0:
            issues.append('行为一致性异常')
        if self._detect_text_patterns(emotion_data.expression_text):
            issues.append('检测到可疑文本模式')
        
        return QualityScore(
            dqs_score=dqs_score,
            quality_level=quality_level,
            weight_coefficient=weight_coefficient,
            completeness_score=completeness_score,
            consistency_score=consistency_score,
            detailed_scores=detailed_scores,
            issues=issues
        )
    
    def _assess_completeness(self, emotion_data: EmotionData) -> float:
        """评估数据完整性（1.1.2节完整性维度）"""
        score = 0.0
        
        # 核心字段完整性（40%）
        field_score = self._check_field_completeness(emotion_data)
        score += field_score * 0.4
        
        # 时间戳准确性（20%）
        if self._is_valid_timestamp(emotion_data.timestamp):
            score += 2.0
        
        # 内容丰富度（25%）
        content_richness = min(2.5, len(emotion_data.expression_text) * 0.1)
        score += content_richness
        
        # 上下文信息（15%）
        if emotion_data.time_context != 'unknown':
            score += 1.5
        
        return min(10.0, score)
    
    def _assess_consistency(self, emotion_data: EmotionData, baseline: Dict[str, Any]) -> float:
        """评估行为一致性（1.1.2节一致性维度）"""
        score = 8.0  # 基础分数
        
        # 情绪-表达一致性（40%）
        emotion_text_consistency = self._check_emotion_text_consistency(emotion_data)
        score += (emotion_text_consistency - 5.0) * 0.8
        
        # 时间模式一致性（30%）
        time_consistency = self._check_time_pattern_consistency(emotion_data)
        score += (time_consistency - 5.0) * 0.6
        
        # 个体基线一致性（30%）
        baseline_consistency = self._calculate_baseline_deviation(emotion_data, baseline)
        score += (baseline_consistency - 5.0) * 0.6
        
        return max(0.0, min(10.0, score))
    
    def _check_field_completeness(self, emotion_data: EmotionData) -> float:
        """检查字段完整性"""
        score = 0.0
        
        # 必需字段检查
        if emotion_data.bstudio_create_time:
            score += 1.0
        if emotion_data.conversation:
            score += 1.0
        if emotion_data.emo_value:
            score += 1.0
        if emotion_data.number:
            score += 1.0
        
        return score
    
    def _check_emotion_text_consistency(self, emotion_data: EmotionData) -> float:
        """检查情绪与文本的一致性"""
        # 简化的一致性检查
        text = emotion_data.expression_text.lower()
        intensity = emotion_data.emotion_intensity
        
        # 积极词汇
        positive_words = ['好', '棒', '喜欢', '开心', '高兴', '满意', '不错', '很棒']
        # 消极词汇
        negative_words = ['不好', '差', '讨厌', '难过', '失望', '糟糕', '烦', '累']
        
        has_positive = any(word in text for word in positive_words)
        has_negative = any(word in text for word in negative_words)
        
        if intensity >= 7.0 and has_positive:
            return 9.0  # 高度一致
        elif intensity <= 3.0 and has_negative:
            return 9.0  # 高度一致
        elif 4.0 <= intensity <= 6.0:
            return 7.0  # 中性，一般一致
        else:
            return 5.0  # 一致性一般
    
    def _check_time_pattern_consistency(self, emotion_data: EmotionData) -> float:
        """检查时间模式一致性"""
        # 简化的时间模式检查
        time_context = emotion_data.time_context
        intensity = emotion_data.emotion_intensity
        
        # 基于时间段的情绪合理性
        if time_context == 'morning' and intensity >= 6.0:
            return 8.0  # 早晨积极情绪较合理
        elif time_context == 'night' and intensity <= 5.0:
            return 7.0  # 夜晚情绪偏低较常见
        else:
            return 6.0  # 一般情况
    
    def _calculate_baseline_deviation(self, emotion_data: EmotionData, baseline: Dict[str, Any]) -> float:
        """计算个体基线偏差（支持冷启动机制）"""
        user_data_count = baseline.get('data_count', 0)
        current_intensity = emotion_data.emotion_intensity
        
        # 冷启动机制：数据量<10时使用默认基线
        if user_data_count < 10:
            # 使用冷启动默认基线
            p50 = self.cold_start_baseline['P50']
            deviation = abs(current_intensity - p50)
            
            # 冷启动时偏差容忍度更高
            if deviation <= 2.0:
                return 8.0
            elif deviation <= 3.0:
                return 6.0
            else:
                return 4.0
        
        # 正常模式：使用个体历史基线
        if 'P25' in baseline and 'P50' in baseline and 'P75' in baseline:
            # 使用P25/P50/P75后验基线
            p25, p50, p75 = baseline['P25'], baseline['P50'], baseline['P75']
            
            if p25 <= current_intensity <= p75:
                return 9.0  # 在正常范围内
            elif abs(current_intensity - p50) <= 1.5:
                return 7.0  # 轻微偏差
            else:
                return 5.0  # 明显偏差
        
        elif 'avg_emotion_intensity' in baseline:
            # 兼容旧格式基线
            avg_intensity = baseline['avg_emotion_intensity']
            std_intensity = baseline.get('emotion_intensity_std', 2.0)
            
            deviation = abs(current_intensity - avg_intensity)
            if deviation <= std_intensity:
                return 8.0
            elif deviation <= std_intensity * 2:
                return 6.0
            else:
                return 4.0
        
        # 无基线数据时的默认处理
        return 6.0
    
    def _is_valid_timestamp(self, timestamp: str) -> bool:
        """验证时间戳格式"""
        try:
            # 检查基本格式
            if not timestamp or len(timestamp) < 10:
                return False
            
            # 检查是否包含日期时间信息
            date_patterns = [r'\d{4}-\d{2}-\d{2}', r'\d{4}/\d{2}/\d{2}']
            time_patterns = [r'\d{2}:\d{2}:\d{2}', r'\d{2}:\d{2}']
            
            has_date = any(re.search(pattern, timestamp) for pattern in date_patterns)
            has_time = any(re.search(pattern, timestamp) for pattern in time_patterns)
            
            return has_date and has_time
        except:
            return False
    
    def _detect_text_patterns(self, context: str) -> bool:
        """检测可疑的文本模式
        
        基于1.1.2节数据质量验证体系的文本模式检测算法：
        1. 检测重复字符（如"哈哈哈"、"啊啊啊"）
        2. 检测明显的测试文本
        3. 检测过短或过长的异常文本
        
        Args:
            context: 待检测的文本内容
            
        Returns:
            bool: True表示检测到可疑模式，False表示正常
        """
        if not context or not isinstance(context, str):
            return True  # 空文本或非字符串视为可疑
        
        context = context.strip()
        
        # 1. 检测重复字符或明显的测试文本
        # 字符种类太少且长度较长（如"哈哈哈哈哈"、"啊啊啊啊"）
        if len(set(context)) < 3 and len(context) > 5:
            return True
        
        # 2. 检测明显的测试文本模式
        test_patterns = [
            'test', '测试', 'aaa', '111', 'xxx', '....',
            'aaaa', 'bbbb', 'cccc', '1111', '2222', '3333',
            'abcd', '1234', 'qwer', 'asdf', 'zxcv',
            '呵呵呵', '嘿嘿嘿', '嗯嗯嗯', '额额额',
            'lalala', 'hahaha', 'hehehe', 'hohoho'
        ]
        
        context_lower = context.lower()
        if any(pattern in context_lower for pattern in test_patterns):
            return True
        
        # 3. 检测过短或过长的异常文本
        if len(context) < 2:  # 过短
            return True
        if len(context) > 500:  # 过长
            return True
        
        # 4. 检测纯数字或纯符号
        if context.isdigit() and len(context) > 3:  # 纯数字且较长
            return True
        
        # 5. 检测重复词汇模式（如"好好好好"、"不不不不"）
        if len(context) >= 6:
            # 检查是否为同一个词的重复
            for word_len in [1, 2, 3]:
                if len(context) % word_len == 0:
                    word = context[:word_len]
                    if word * (len(context) // word_len) == context:
                        return True
        
        return False
    
    def _determine_quality_level(self, dqs_score: float) -> Tuple[str, float]:
        """根据DQS分数确定质量等级和权重系数"""
        for level, (min_score, max_score, weight_coeff) in self.quality_thresholds.items():
            if min_score <= dqs_score <= max_score:
                return level, weight_coeff
        return "D", 0.1  # 默认返回D级

def _get_processing_decision(quality_level: str) -> str:
    """根据质量等级获取处理决策"""
    decisions = {
        'A': '优先选取，直接进入核心存储',
        'B': '正常选取，进入标准存储流程',
        'C': '条件选取，容量不足时使用',
        'D': '直接舍弃，不进入存储系统'
    }
    return decisions.get(quality_level, '未知处理策略')

def _get_current_timestamp() -> str:
    """获取当前时间戳（简化版本，避免使用datetime模块）"""
    # 由于Coze平台限制，使用简化的时间戳
    import time
    return str(int(time.time()))

# 主函数 - 符合Coze平台要求的async格式
async def main(args: Args) -> Output:
    """1.1.2节 数据质量验证体系主函数
    
    输入参数：
    - outputList: 原始情绪数据列表，每条数据可包含基线字段：
        * 基础字段：bstudio_create_time, conversation, emo_value, number
        * 基线字段（可选）：P25, P50, P75, data_count
        * 兼容字段（可选）：avg_emotion_intensity, emotion_intensity_std
    
    示例格式：
    [
        {
            "bstudio_create_time": "2024-01-15 14:30:00",
            "conversation": "今天心情不错",
            "emo_value": "8.5",
            "number": "15",
            "P25": 6.2,      # 可选：25%分位数基线
            "P50": 7.8,      # 可选：50%分位数基线
            "P75": 8.9,      # 可选：75%分位数基线
            "data_count": 25  # 可选：用户历史数据量
        }
    ]
    
    输出结果：
    - validation_results: 验证结果列表
    - statistics: 质量统计信息
    - quality_thresholds: 质量阈值标准
    - algorithm_info: 算法信息
    - processing_summary: 处理摘要（包含模式信息）
    """
    # 直接从args.params获取outputList参数
    output_list = args.params.get('outputList', [])
    
    # 初始化验证器
    validator = DataQualityValidator()
    
    # 处理数据
    results = []
    statistics = {
        'total_count': 0,
        'level_distribution': {'A': 0, 'B': 0, 'C': 0, 'D': 0},
        'level_percentages': {'A': 0.0, 'B': 0.0, 'C': 0.0, 'D': 0.0},
        'average_dqs': 0.0,
        'average_completeness': 0.0,
        'average_consistency': 0.0,
        'quality_rate': {'A_B_rate': 0.0, 'usable_rate': 0.0}
    }
    
    if output_list:
        total_dqs = 0
        total_completeness = 0
        total_consistency = 0
        
        for data_dict in output_list:
            try:
                # 转换为EmotionData对象（使用实际数据格式）
                emotion_data = EmotionData(
                    bstudio_create_time=data_dict.get('bstudio_create_time', ''),
                    conversation=data_dict.get('conversation', ''),
                    emo_value=data_dict.get('emo_value', '5'),
                    number=data_dict.get('number', '0')
                )
                
                # 从当前数据中提取基线信息
                baseline = {}
                if 'P25' in data_dict and 'P50' in data_dict and 'P75' in data_dict:
                    # 使用P25/P50/P75格式
                    baseline = {
                        'P25': float(data_dict.get('P25', 4.0)),
                        'P50': float(data_dict.get('P50', 5.0)),
                        'P75': float(data_dict.get('P75', 6.0)),
                        'data_count': int(data_dict.get('data_count', 0))
                    }
                elif 'avg_emotion_intensity' in data_dict:
                    # 兼容旧格式
                    baseline = {
                        'avg_emotion_intensity': float(data_dict.get('avg_emotion_intensity', 5.0)),
                        'emotion_intensity_std': float(data_dict.get('emotion_intensity_std', 2.0)),
                        'data_count': int(data_dict.get('data_count', 0))
                    }
                else:
                    # 无基线数据，使用空字典（将触发冷启动）
                    baseline = {'data_count': 0}
                
                # 验证质量
                quality_score = validator.validate_data_quality(emotion_data, baseline)
                
                # 获取用户数据量（用于冷启动判断）
                user_data_count = baseline.get('data_count', 0)
                is_cold_start = user_data_count < 10
                
                # 转换为字典格式输出（移除原始输入参数）
                result_dict = {
                    'data_id': emotion_data.session_id,  # 数据标识
                    'user_id': emotion_data.user_id,
                    'emotion_analysis': {
                        'emotion_type': emotion_data.emotion_type,
                        'emotion_intensity': emotion_data.emotion_intensity,
                        'expression_text': emotion_data.expression_text,
                        'expression_length': emotion_data.expression_length,
                        'expression_complexity': round(emotion_data.expression_complexity, 2),
                        'time_context': emotion_data.time_context,
                        'timestamp': emotion_data.timestamp
                    },
                    'quality_assessment': {
                        'dqs_score': round(quality_score.dqs_score, 2),
                        'quality_level': quality_score.quality_level,
                        'weight_coefficient': quality_score.weight_coefficient,
                        'completeness_score': round(quality_score.completeness_score, 2),
                        'consistency_score': round(quality_score.consistency_score, 2),
                        'processing_decision': _get_processing_decision(quality_score.quality_level)
                    },
                    'baseline_info': {
                        'is_cold_start': is_cold_start,
                        'user_data_count': user_data_count,
                        'baseline_mode': '冷启动默认基线' if is_cold_start else '个体历史基线'
                    },
                    'detailed_scores': {
                        k: round(v, 2) if isinstance(v, (int, float)) else v 
                        for k, v in quality_score.detailed_scores.items() 
                        if k != 'issues'
                    },
                    'issues': quality_score.issues
                }
                
                results.append(result_dict)
                
                # 更新统计
                statistics['level_distribution'][quality_score.quality_level] += 1
                total_dqs += quality_score.dqs_score
                total_completeness += quality_score.completeness_score
                total_consistency += quality_score.consistency_score
                
            except Exception as e:
                # 处理数据转换错误
                error_result = {
                    'original_data': data_dict,
                    'error': f"数据处理错误: {str(e)}",
                    'quality_assessment': {
                        'dqs_score': 0.0,
                        'quality_level': 'D',
                        'weight_coefficient': 0.1,
                        'processing_decision': '数据格式错误，直接舍弃'
                    }
                }
                results.append(error_result)
                statistics['level_distribution']['D'] += 1
        
        # 计算统计信息
        count = len(results)
        statistics['total_count'] = count
        
        if count > 0:
            statistics['average_dqs'] = round(total_dqs / count, 2)
            statistics['average_completeness'] = round(total_completeness / count, 2)
            statistics['average_consistency'] = round(total_consistency / count, 2)
            
            # 计算百分比
            for level in ['A', 'B', 'C', 'D']:
                statistics['level_percentages'][level] = round(
                    statistics['level_distribution'][level] / count * 100, 1
                )
            
            # 计算质量率
            a_b_count = statistics['level_distribution']['A'] + statistics['level_distribution']['B']
            usable_count = a_b_count + statistics['level_distribution']['C']
            
            statistics['quality_rate']['A_B_rate'] = round(a_b_count / count * 100, 1)
            statistics['quality_rate']['usable_rate'] = round(usable_count / count * 100, 1)
    
    # 构建输出
    ret: Output = {
        'validation_results': results,
        'statistics': statistics,
        'quality_thresholds': {
            'A_level': '8.0-10.0分，优先选取，权重系数1.0',
            'B_level': '6.0-7.9分，正常选取，权重系数0.8', 
            'C_level': '4.0-5.9分，容量不足时选取，权重系数0.5',
            'D_level': '0.0-3.9分，直接舍弃，权重系数0.1'
        },
        'algorithm_info': {
            'section': '1.1.2 数据质量验证体系',
            'model': '简化的二维质量评分模型',
            'theory_basis': '心理测量学信度理论',
            'completeness_weight': 0.6,
            'consistency_weight': 0.4,
            'evaluation_dimensions': {
                'completeness': ['核心字段完整性', '时间戳准确性', '内容丰富度', '上下文信息'],
                'consistency': ['情绪-表达一致性', '时间模式一致性', '个体基线一致性']
            }
        },
        'processing_summary': {
            'total_processed': len(results),
            'timestamp': _get_current_timestamp()
        }
    }
    
    return ret

# 注意：此代码专为Coze平台设计，平台会自动调用main函数
# 如需本地测试，请使用单独的测试文件