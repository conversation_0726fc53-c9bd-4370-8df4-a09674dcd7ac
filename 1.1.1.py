# 优化版数据预处理与质量控制系统 - 适配环境限制版本
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from collections import defaultdict
from enum import Enum

# 数据结构定义
class QG(Enum):
    A, B, C, D = "优秀", "良好", "一般", "较差"

class ML(Enum):
    IM, WM, SM, LM, CM = "即时记忆", "工作记忆", "短期记忆", "长期记忆", "核心记忆"

@dataclass
class ER:
    uid: str
    content: str
    ts: datetime
    es: float
    qs: float = 0.0
    wf: float = 1.0
    anom: bool = False
    original_data: Optional[Dict] = None
    
    @classmethod
    def from_json(cls, data: Dict, uid: str):
        try:
            # 安全处理时间格式
            time_str = data.get('bstudio_create_time', '')
            if not time_str or time_str.strip() == '':
                ts = datetime.now()  # 使用当前时间作为默认值
            else:
                time_str = time_str.replace(' +0800 CST', '').replace(' +0800', '')
                ts = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
            
            # 安全处理emo_value转换
            emo_value = data.get('emo_value', '5')
            if isinstance(emo_value, str) and emo_value.strip() == '':
                emo_value = '5'  # 默认值
            
            return cls(
                uid=uid,
                content=data.get('conversation', ''),
                ts=ts,
                es=float(emo_value),
                original_data=data
            )
        except Exception as e:
            # 如果解析失败，返回一个默认的记录
            return cls(
                uid=uid,
                content=data.get('conversation', ''),
                ts=datetime.now(),
                es=5.0,
                original_data=data
            )
    
    def to_dict(self) -> Dict:
        if self.original_data:
            result = self.original_data.copy()
            result['uid'] = self.uid
            result['quality_score'] = float(self.qs)
            result['anomaly'] = bool(self.anom)
            return result
        return {
            'uid': self.uid,
            'content': self.content,
            'timestamp': self.ts.isoformat(),
            'emotion_score': float(self.es),
            'quality_score': float(self.qs),
            'anomaly': bool(self.anom)
        }

@dataclass
class PT:
    uid: str
    trait_type: str
    value: Any
    confidence: float
    extracted_at: datetime = field(default_factory=datetime.now)

# 核心处理类
class DQV:
    def __init__(self):
        self.weights = {'content': 0.4, 'emotion': 0.3, 'context': 0.3}
    
    def calc_qs(self, record: ER, baseline: Dict) -> float:
        content_score = min(len(record.content) / 20, 1.0) * 10
        emotion_score = 10 - abs(record.es - baseline.get('avg_emotion_score', 5.0))
        context_score = 8.0 if record.ts.hour in baseline.get('active_hours', []) else 5.0
        
        return float(content_score * self.weights['content'] + 
                     emotion_score * self.weights['emotion'] + 
                     context_score * self.weights['context'])
    
    def get_grade(self, score: float) -> QG:
        if score >= 8.0: return QG.A
        elif score >= 6.0: return QG.B
        elif score >= 4.0: return QG.C
        else: return QG.D

class AAD:
    def __init__(self):
        self.threshold = 2.0
    
    def detect_anomalies(self, records: List[ER], baseline: Dict) -> List[ER]:
        if len(records) < 2:
            return records
        
        scores = [r.qs for r in records]
        mean_score = float(np.mean(scores))
        std_score = float(np.std(scores))
        
        for record in records:
            z_score = abs(record.qs - mean_score) / (std_score + 1e-6)
            record.anom = bool(z_score > self.threshold)
        
        return records

class LMM:
    def __init__(self):
        self.immediate_memory = []
        self.working_memory = []
        self.short_memory = []
        self.long_memory = []
        self.core_memory = []
        self.limits = {'immediate': 100, 'working': 500, 'short': 1000, 'long': 2000, 'core': 5000}
    
    def store_record(self, record: ER) -> bool:
        try:
            age_days = (datetime.now() - record.ts).days
            
            if age_days <= 1:
                self._add_to_layer('immediate', record)
            elif age_days <= 7:
                self._add_to_layer('working', record)
            elif age_days <= 28:
                self._add_to_layer('short', record)
            elif age_days <= 112:
                self._add_to_layer('long', record)
            else:
                self._add_to_layer('core', record)
            
            return True
        except:
            return False
    
    def _add_to_layer(self, layer_name: str, record: ER):
        layer = getattr(self, f'{layer_name}_memory')
        layer.append(record.to_dict())
        
        if len(layer) > self.limits[layer_name]:
            self._promote_oldest(layer_name)
    
    def _promote_oldest(self, layer_name: str):
        layer = getattr(self, f'{layer_name}_memory')
        if layer:
            oldest = layer.pop(0)
            next_layers = {'immediate': 'working', 'working': 'short', 'short': 'long', 'long': 'core'}
            if layer_name in next_layers:
                next_layer = getattr(self, f'{next_layers[layer_name]}_memory')
                next_layer.append(oldest)
    
    def get_all_arrays(self) -> Dict[str, List]:
        return {
            '即时记忆': self.immediate_memory.copy(),
            '工作记忆': self.working_memory.copy(),
            '短期记忆': self.short_memory.copy(),
            '长期记忆': self.long_memory.copy(),
            '核心记忆': self.core_memory.copy()
        }

class PTE:
    def extract_traits(self, content: str, uid: str) -> List[PT]:
        traits = []
        
        if len(content) > 50:
            traits.append(PT(uid, 'verbosity', 'high', 0.8))
        
        emotion_words = ['开心', '难过', '愤怒', '兴奋', '焦虑']
        for word in emotion_words:
            if word in content:
                traits.append(PT(uid, 'emotion_expression', word, 0.9))
        
        return traits

class DSA:
    def __init__(self):
        self.standards = {
            'rec': {'quantity': 100, 'timespan': 30, 'coverage': 0.5}
        }
    
    def assess_data_sufficiency(self, uid: str, records: List[ER]) -> Dict:
        stats = self._calc_stats(records)
        
        qs = self._assess_quantity(stats)
        ds = self._assess_diversity(stats)
        qls = self._assess_quality(stats)
        ts = self._assess_temporal(stats)
        
        overall = (qs + ds + qls + ts) / 4
        stage = self._determine_stage(overall)
        recs = self._gen_recommendations(qs, ds, qls, ts)
        
        return {
            'overall_score': round(overall, 2),
            'data_stage': stage,
            'quantity_score': round(qs, 2),
            'diversity_score': round(ds, 2),
            'quality_score': round(qls, 2),
            'temporal_score': round(ts, 2),
            'recommendations': recs
        }
    
    def _calc_stats(self, records: List[ER]) -> Dict:
        if not records: return {}
        
        ts_list = [r.ts for r in records]
        timespan = (max(ts_list) - min(ts_list)).days
        
        return {
            'count': len(records),
            'timespan': timespan,
            'avg_quality': sum(r.qs for r in records) / len(records),
            'emotion_range': max(r.es for r in records) - min(r.es for r in records),
            'unique_hours': len(set(r.ts.hour for r in records))
        }
    
    def _assess_quantity(self, stats: Dict) -> float:
        if not stats: return 0.0
        count_score = min(stats['count'] / self.standards['rec']['quantity'], 1.0) * 5
        timespan_score = min(stats['timespan'] / self.standards['rec']['timespan'], 1.0) * 5
        return count_score + timespan_score
    
    def _assess_diversity(self, stats: Dict) -> float:
        if not stats: return 0.0
        emotion_diversity = min(stats['emotion_range'] / 8.0, 1.0) * 5
        time_diversity = min(stats['unique_hours'] / 16.0, 1.0) * 5
        return emotion_diversity + time_diversity
    
    def _assess_quality(self, stats: Dict) -> float:
        if not stats: return 0.0
        return min(stats['avg_quality'], 10.0)
    
    def _assess_temporal(self, stats: Dict) -> float:
        if not stats: return 0.0
        coverage = stats['unique_hours'] / 24.0
        return min(coverage / self.standards['rec']['coverage'], 1.0) * 10
    
    def _determine_stage(self, score: float) -> str:
        if score >= 8.0: return '充分'
        elif score >= 6.0: return '基本充分'
        elif score >= 4.0: return '不充分'
        else: return '严重不足'
    
    def _gen_recommendations(self, qs, ds, qls, ts) -> List[str]:
        recs = []
        if qs < 6.0: recs.append('增加数据收集频率')
        if ds < 6.0: recs.append('扩大情绪和时间覆盖范围')
        if qls < 6.0: recs.append('提高数据质量标准')
        if ts < 6.0: recs.append('改善时间分布均匀性')
        return recs

class DPP:
    def __init__(self):
        self.qv = DQV()
        self.ad = AAD()
        self.mm = LMM()
        self.te = PTE()
        self.sa = DSA()
    
    async def process_records(self, records: List[ER]) -> Dict[str, Any]:
        results = {
            'processed_count': 0,
            'quality_distribution': defaultdict(int),
            'anomaly_count': 0,
            'storage_success': 0,
            'extracted_traits': 0,
            'sufficiency_assessments': {},
            'layered_storage': {
                'immediate_memory': [],
                'working_memory': [],
                'short_memory': [],
                'long_memory': [],
                'core_memory': []
            }
        }
        
        user_groups = defaultdict(list)
        for record in records:
            user_groups[record.uid].append(record)
        
        for uid, user_records in user_groups.items():
            suff_result = self.sa.assess_data_sufficiency(uid, user_records)
            results['sufficiency_assessments'][uid] = suff_result
            
            ub = await self._get_user_baseline(uid)
            
            for record in user_records:
                record.qs = self.qv.calc_qs(record, ub)
                grade = self.qv.get_grade(record.qs)
                results['quality_distribution'][grade.value] += 1
                record.wf = self._get_weight_factor(grade)
            
            user_records = self.ad.detect_anomalies(user_records, ub)
            results['anomaly_count'] += sum(1 for r in user_records if r.anom)
            
            for record in user_records:
                if self.mm.store_record(record):
                    results['storage_success'] += 1
                
                traits = self.te.extract_traits(record.content, uid)
                results['extracted_traits'] += len(traits)
                
                for trait in traits:
                    await self._store_personal_trait(trait)
            
            results['processed_count'] += len(user_records)
        
        # 收集所有用户的分层存储数据
        all_layered_data = self.mm.get_all_arrays()
        # 映射中文层名到英文键名
        layer_mapping = {
            '即时记忆': 'immediate_memory',
            '工作记忆': 'working_memory', 
            '短期记忆': 'short_memory',
            '长期记忆': 'long_memory',
            '核心记忆': 'core_memory'
        }
        for layer_name, layer_records in all_layered_data.items():
            english_key = layer_mapping.get(layer_name, layer_name)
            results['layered_storage'][english_key] = layer_records
        
        return results
    
    async def _get_user_baseline(self, uid: str) -> Dict:
        return {
            'avg_emotion_score': 5.0,
            'avg_message_length': 50,
            'active_hours': [9, 10, 11, 14, 15, 16, 20, 21]
        }
    
    def _get_weight_factor(self, grade: QG) -> float:
        weights = {QG.A: 1.0, QG.B: 0.8, QG.C: 0.5, QG.D: 0.1}
        return weights.get(grade, 0.5)
    
    async def _store_personal_trait(self, trait: PT):
        pass

# 主函数 - 符合环境要求的格式
async def main(args) -> Dict:
    # 获取输入参数
    params = args.params if hasattr(args, 'params') else args
    input_data = params.get('input', params.get('outputList', []))
    
    # 如果输入是字符串，尝试解析为JSON
    if isinstance(input_data, str):
        try:
            input_data = json.loads(input_data)
        except:
            input_data = []
    
    # 创建处理管道
    pipeline = DPP()
    
    # 处理数据
    if input_data:
        sample_records = [ER.from_json(item, "user_001") for item in input_data]
        results = await pipeline.process_records(sample_records)
    else:
        # 使用示例数据
        outputList = [
            {
                "bstudio_create_time": "2025-06-13 22:50:15 +0800 CST",
                "emo_value": "7",
                "number": "4",
                "conversation": "好开心呀"
            },
            {
                "bstudio_create_time": "2025-06-14 18:04:27 +0800 CST",
                "emo_value": "5",
                "number": "2",
                "conversation": "修够"
            },
            {
                "bstudio_create_time": "2025-06-15 22:39:22 +0800 CST",
                "emo_value": "2",
                "number": "2",
                "conversation": "差评"
            }
        ]
        sample_records = [ER.from_json(item, "user_001") for item in outputList]
        results = await pipeline.process_records(sample_records)
    
    # 构建输出结果
    ret = {
        "processing_summary": {
            "processed_count": results['processed_count'],
            "storage_success": results['storage_success'],
            "anomaly_count": results['anomaly_count'],
            "extracted_traits": results['extracted_traits']
        },
        "quality_distribution": dict(results['quality_distribution']),
        "sufficiency_assessments": results['sufficiency_assessments'],
        "immediate_memory": results['layered_storage']['immediate_memory'],
        "working_memory": results['layered_storage']['working_memory'],
        "short_memory": results['layered_storage']['short_memory'],
        "long_memory": results['layered_storage']['long_memory'],
        "core_memory": results['layered_storage']['core_memory']
    }
    
    return ret