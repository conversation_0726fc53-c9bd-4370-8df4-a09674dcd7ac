"""
计算7模块主函数
基于输入参数进行关系健康度评估
"""

import time
from datetime import datetime
from typing import Dict, List, Any

# 用户类型健康特征
USER_HEALTH_FEATURES = {
    "积极稳定型": {"advantages": ["情绪稳定", "投入持续"], "features": "稳定基础上的持续健康", "coefficient": 1.0},
    "沉稳内敛型": {"advantages": ["稳定性强", "持续性好"], "features": "内在稳定的外在表达", "coefficient": 0.95},
    "情绪敏感型": {"advantages": ["表达丰富", "反应敏锐"], "features": "敏感性的积极转化", "coefficient": 1.05},
    "消极波动型": {"advantages": ["变化敏感", "适应性强"], "features": "波动中的稳定寻求", "coefficient": 1.1},
    "适应调整型": {"advantages": ["灵活性强", "学习能力好"], "features": "适应性的模式化发展", "coefficient": 1.02}
}

# 健康度等级
HEALTH_LEVELS = [
    {"min": 0.9, "level": "卓越健康", "numeric": 5, "grade": "极优"},
    {"min": 0.8, "level": "优秀健康", "numeric": 4, "grade": "优秀"},
    {"min": 0.7, "level": "良好健康", "numeric": 3, "grade": "良好"},
    {"min": 0.6, "level": "一般健康", "numeric": 2, "grade": "一般"},
    {"min": 0.0, "level": "待改善", "numeric": 1, "grade": "需改善"}
]

def safe_float(value: str, default: float = 0.0) -> float:
    """安全转换字符串为浮点数"""
    try:
        if not value or value.lower() in ['', 'null', 'none']:
            return default
        return float(value)
    except (ValueError, TypeError):
        return default

def calculate_s_health_factor(rsi_value: float, cem_value: float, user_type: str) -> float:
    """计算S参数健康因子"""
    # 基础情绪健康度
    base_emotion_health = min(1.0, rsi_value / 0.7)  # RSI达到0.7时为满分

    # CEM动量对情绪健康的贡献
    normalized_cem = max(0, (cem_value + 1.0) / 2.0)
    cem_contribution = normalized_cem * 0.3

    # 用户类型调整
    user_features = USER_HEALTH_FEATURES.get(user_type, USER_HEALTH_FEATURES["沉稳内敛型"])
    type_coefficient = user_features["coefficient"]

    s_health = (base_emotion_health * 0.7 + cem_contribution) * type_coefficient
    return min(1.0, max(0.0, s_health))

def calculate_m_health_factor(rsi_value: float, adaptability_score: float, user_type: str) -> float:
    """计算M参数健康因子"""
    # 基础投入健康度
    base_engagement_health = min(1.0, rsi_value / 0.6)  # RSI达到0.6时为满分

    # 适应性对投入健康的贡献
    adaptation_contribution = adaptability_score * 0.4

    # 用户类型调整
    user_features = USER_HEALTH_FEATURES.get(user_type, USER_HEALTH_FEATURES["沉稳内敛型"])
    type_coefficient = user_features["coefficient"]

    m_health = (base_engagement_health * 0.6 + adaptation_contribution) * type_coefficient
    return min(1.0, max(0.0, m_health))

def calculate_t_health_factor(rsi_value: float, cem_grade: str, user_type: str) -> float:
    """计算T参数健康因子"""
    # 基础时间健康度
    base_time_health = min(1.0, rsi_value / 0.65)  # RSI达到0.65时为满分

    # 趋势对时间健康的贡献
    trend_scores = {
        "急剧上升": 0.3, "稳定上升": 0.25, "温和上升": 0.2,
        "保持稳定": 0.15, "温和下降": 0.1, "稳定下降": 0.05, "急剧下降": 0.0
    }
    trend_contribution = trend_scores.get(cem_grade, 0.1)

    # 用户类型调整
    user_features = USER_HEALTH_FEATURES.get(user_type, USER_HEALTH_FEATURES["沉稳内敛型"])
    type_coefficient = user_features["coefficient"]

    t_health = (base_time_health * 0.7 + trend_contribution) * type_coefficient
    return min(1.0, max(0.0, t_health))

def calculate_comprehensive_health(s_health: float, m_health: float, t_health: float,
                                 adaptability_score: float, crisis_probability: float) -> float:
    """计算综合健康度"""
    # 基础健康度
    base_health = s_health * 0.4 + m_health * 0.35 + t_health * 0.25

    # 适应性调整
    adaptation_adjustment = adaptability_score * 0.1

    # 安全性调整
    safety_adjustment = max(0, (1.0 - crisis_probability)) * 0.1

    # 综合健康度
    comprehensive_health = base_health * 0.8 + adaptation_adjustment + safety_adjustment
    return min(1.0, max(0.0, comprehensive_health))

def determine_health_level(health_score: float) -> Dict:
    """确定健康度等级"""
    for level_info in HEALTH_LEVELS:
        if health_score >= level_info['min']:
            return level_info
    return HEALTH_LEVELS[-1]

def analyze_health_characteristics(s_health: float, m_health: float, t_health: float, user_type: str) -> Dict:
    """分析健康特征"""
    # 找出优势维度
    health_factors = {
        "情绪健康": s_health,
        "投入健康": m_health,
        "时间健康": t_health
    }
    dominant_strength = max(health_factors, key=health_factors.get)

    # 识别改善空间
    improvement_areas = []
    if s_health < 0.7:
        improvement_areas.append("情绪表达丰富度")
    if m_health < 0.7:
        improvement_areas.append("投入质量提升")
    if t_health < 0.7:
        improvement_areas.append("时间效率优化")

    # 获取用户健康特征
    user_features = USER_HEALTH_FEATURES.get(user_type, USER_HEALTH_FEATURES["沉稳内敛型"])

    return {
        "s_health_factor": s_health,
        "m_health_factor": m_health,
        "t_health_factor": t_health,
        "dominant_strength": dominant_strength,
        "improvement_areas": improvement_areas,
        "user_health_pattern": user_features["features"]
    }

def calculate_comprehensive_confidence(rsi_conf: float, cem_conf: float, eii_conf: float, crisis_conf: float) -> float:
    """计算综合置信度"""
    # 加权平均计算
    weights = [0.3, 0.3, 0.3, 0.1]  # RSI, CEM, EII, 危机
    confidences = [rsi_conf, cem_conf, eii_conf, crisis_conf]
    
    weighted_sum = sum(c * w for c, w in zip(confidences, weights) if c > 0)
    weight_sum = sum(w for c, w in zip(confidences, weights) if c > 0)
    
    return weighted_sum / weight_sum if weight_sum > 0 else 0.3

async def main(args) -> Dict:
    """
    计算7：关系健康度评估模块主函数

    输入参数：
    - rsi_value: RSI关系稳定指数
    - confidence_level: 综合置信度等级
    - cem_value: CEM动量值
    - cem_grade: CEM等级
    - cem_confidence: CEM置信度
    - adaptability_score: 适应性评分
    - eii_confidence: EII置信度
    - crisis_probability: 危机概率
    - crisis_confidence: 危机置信度
    - user_type: 用户类型
    """
    start_time = time.time()

    try:
        # 获取输入参数
        params = args.params

        # 1. 基础数据转换
        rsi_value = safe_float(params.get('rsi_value', '0.0'))
        cem_value = safe_float(params.get('cem_value', '0.0'))
        cem_grade = params.get('cem_grade', '保持稳定')
        adaptability_score = safe_float(params.get('adaptability_score', '0.5'))
        crisis_probability = safe_float(params.get('crisis_probability', '0.3'))
        user_type = params.get('user_type', '沉稳内敛型')

        # 置信度参数
        rsi_confidence = safe_float(params.get('confidence_level', '0.5'))
        cem_confidence = safe_float(params.get('cem_confidence', '0.5'))
        eii_confidence = safe_float(params.get('eii_confidence', '0.5'))
        crisis_confidence = safe_float(params.get('crisis_confidence', '0.5'))
        
        # 2. 计算三个健康因子
        s_health_factor = calculate_s_health_factor(rsi_value, cem_value, user_type)
        m_health_factor = calculate_m_health_factor(rsi_value, adaptability_score, user_type)
        t_health_factor = calculate_t_health_factor(rsi_value, cem_grade, user_type)

        # 3. 计算综合健康度
        comprehensive_health = calculate_comprehensive_health(
            s_health_factor, m_health_factor, t_health_factor,
            adaptability_score, crisis_probability
        )

        # 4. 确定健康等级
        health_level_info = determine_health_level(comprehensive_health)

        # 5. 分析健康特征
        health_analysis = analyze_health_characteristics(
            s_health_factor, m_health_factor, t_health_factor, user_type
        )

        # 6. 计算置信度
        comprehensive_confidence = calculate_comprehensive_confidence(
            rsi_confidence, cem_confidence, eii_confidence, crisis_confidence
        )

        # 7. 计算处理时间
        processing_time = round((time.time() - start_time) * 1000, 2)

        # 8. 构建输出结果
        ret = {
            "calculation_id": f"health_calc_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "calculation_type": "health_assessment",
            "version": "7.1.0",
            "timestamp": datetime.now().isoformat(),

            "health_assessment": {
                "health_score": round(comprehensive_health, 3),
                "health_grade": health_level_info['level'],
                "health_level_numeric": health_level_info['numeric'],
                "overall_evaluation": f"关系健康度{health_level_info['grade']}"
            },

            "health_analysis": {
                "s_health_factor": round(health_analysis["s_health_factor"], 3),
                "m_health_factor": round(health_analysis["m_health_factor"], 3),
                "t_health_factor": round(health_analysis["t_health_factor"], 3),
                "dominant_strength": health_analysis["dominant_strength"],
                "improvement_areas": health_analysis["improvement_areas"],
                "user_health_pattern": health_analysis["user_health_pattern"]
            },

            "health_context": {
                "health_level_numeric": health_level_info['numeric'],
                "health_grade": health_level_info['level'],
                "dominant_strength": health_analysis["dominant_strength"],
                "improvement_areas": health_analysis["improvement_areas"],
                "user_health_characteristics": {
                    "type_advantages": USER_HEALTH_FEATURES[user_type]["advantages"],
                    "health_features": USER_HEALTH_FEATURES[user_type]["features"],
                    "health_pattern": f"{user_type}健康模式"
                },
                "strategy_guidance": {
                    "priority_level": f"H{health_level_info['numeric']}-{health_level_info['level']}",
                    "focus_dimensions": health_analysis["improvement_areas"][:2] if health_analysis["improvement_areas"] else [health_analysis["dominant_strength"]],
                    "health_direction": "稳步提升" if comprehensive_health < 0.8 else "保持优化"
                }
            },

            "confidence_breakdown": {
                "l1_input_quality": round(comprehensive_confidence, 3),
                "l2_calculation_stability": 0.9,
                "l3_output_confidence": round(comprehensive_confidence * 0.9, 3)
            },

            "validation_result": {
                "health_validation": True,
                "factor_consistency": True,
                "health_assessment": True,
                "performance_check": processing_time <= 80
            },

            "metadata": {
                "assessment_basis": "S/M/T健康因子模型",
                "assessment_mode": "标准健康度评估",
                "user_type": user_type,
                "processing_time_ms": processing_time,
                "data_basis": "RSI稳定性+CEM动量+适应性+危机概率",
                "unique_output": "关系健康度专业评估"
            }
        }

        return ret

    except Exception as e:
        # 错误处理：返回默认的健康度评估结果
        return {
            "calculation_id": f"health_calc_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "calculation_type": "health_assessment",
            "version": "7.1.0",
            "error_info": {
                "error_occurred": True,
                "error_message": str(e),
                "fallback_mode": "默认健康度评估"
            },
            "health_assessment": {
                "health_score": 0.6,
                "health_grade": "一般健康",
                "health_level_numeric": 2,
                "overall_evaluation": "关系健康度一般"
            },
            "health_context": {
                "health_level_numeric": 2,
                "health_grade": "一般健康",
                "strategy_guidance": {
                    "priority_level": "H2-一般健康",
                    "focus_dimensions": ["均衡发展"],
                    "health_direction": "稳步提升"
                }
            },
            "metadata": {
                "assessment_mode": "错误降级模式",
                "processing_time_ms": round((time.time() - start_time) * 1000, 2),
                "unique_output": "关系健康度专业评估"
            }
        }
