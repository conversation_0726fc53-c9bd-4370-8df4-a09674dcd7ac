import math
from datetime import datetime
from typing import Any, Dict, List

async def main(args: Any) -> Dict[str, Any]:
    """
    数据充分性评估函数
    """
    params = args.params
    output_list: List[Dict[str, Any]] = params['outputList']
    quality_grade: str = params['quality_grade']
    
    # 1. 数据量计算 f_N
    N = len(output_list)
    
    # S型函数计算
    f_N = 1 / (1 + math.exp(-0.1 * (N - 50)))
    
    # 2. 时间跨度计算 f_T
    times = []
    for item in output_list:
        # 截取有效时间字符串
        ts_str = item['bstudio_create_time'][:19]
        try:
            dt = datetime.strptime(ts_str, "%Y-%m-%d %H:%M:%S")
            times.append(dt)
        except ValueError:
            continue
    
    T_days = 0
    if len(times) >= 2:
        earliest = min(times)
        latest = max(times)
        T_days = (latest - earliest).days
    
    f_T = min(T_days / 60, 1.0)
    
    # 3. 时段覆盖计算 f_C
    hours_covered = set(dt.hour for dt in times)
    coverage_percent = len(hours_covered) / 24
    f_C = coverage_percent
    
    # 4. 质量评分映射 f_Q
    # 提取等级部分
    grade_level = quality_grade.split("（")[0]
    
    # 等级到分数的映射
    grade_to_score = {
        "A级": 8,
        "B级": 6,
        "C级": 4
    }
    
    score_Q = grade_to_score.get(grade_level, 5)  # 默认中间值
    f_Q = (score_Q - 4) / 6  # 标准化到0-1范围
    
    # 5. 计算DSI指数
    DSI = (
        f_N * 0.4 + 
        f_T * 0.3 + 
        f_C * 0.15 + 
        f_Q * 0.15
    )
    
    # 6. 充分性等级判断
    if DSI >= 0.8:
        sufficiency_level = "充分"
    elif DSI >= 0.6:
        sufficiency_level = "基本充分"
    elif DSI >= 0.4:
        sufficiency_level = "不充分"
    else:
        sufficiency_level = "严重不足"
    
    # 返回结果（可包含调试信息）
    return {
        "sufficiency_level": sufficiency_level,
        "DSI": round(DSI, 4),
        "data_count": N,
        "time_span_days": T_days,
        "coverage_percent": round(coverage_percent * 100, 2),
        "quality_contribution": round(f_Q, 4)
    }