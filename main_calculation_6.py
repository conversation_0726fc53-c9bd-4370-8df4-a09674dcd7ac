"""
计算6模块主函数
基于输入参数进行危机分数计算
"""

import time
from datetime import datetime
from typing import Dict, List, Any

# 用户类型危机倾向系数
USER_TYPE_COEFFICIENTS = {
    "积极稳定型": 0.8, "沉稳内敛型": 0.9, "情绪敏感型": 1.2,
    "消极波动型": 1.3, "适应调整型": 1.1
}

# 风险等级定义
RISK_LEVELS = [
    {"min": 0.8, "level": "极高风险", "warning": "红色预警", "urgency": "紧急干预"},
    {"min": 0.6, "level": "高风险", "warning": "橙色预警", "urgency": "重点关注"},
    {"min": 0.4, "level": "中风险", "warning": "黄色预警", "urgency": "积极干预"},
    {"min": 0.2, "level": "低风险", "warning": "蓝色预警", "urgency": "预防性关注"},
    {"min": 0.0, "level": "安全", "warning": "无预警", "urgency": "常规监控"}
]

def safe_float(value: str, default: float = 0.0) -> float:
    """安全转换字符串为浮点数"""
    try:
        if not value or value.lower() in ['', 'null', 'none']:
            return default
        return float(value)
    except (ValueError, TypeError):
        return default

def calculate_comprehensive_confidence(params: Dict) -> float:
    """计算综合置信度"""
    # confidence_level 对应 RSI相关的置信度
    rsi_conf = safe_float(params.get('confidence_level', '0.0'))
    # eii_confidence 从计算5的 l3_output_confidence 获取
    eii_conf = safe_float(params.get('eii_confidence', '0.0'))

    if rsi_conf > 0 and eii_conf > 0:
        return rsi_conf * 0.6 + eii_conf * 0.4
    elif rsi_conf > 0:
        return rsi_conf * 0.8
    elif eii_conf > 0:
        return eii_conf * 0.8
    else:
        return 0.3

def determine_assessment_mode(confidence: float) -> str:
    """确定评估模式"""
    if confidence >= 0.5:
        return "标准评估"
    elif confidence >= 0.3:
        return "简化评估"
    else:
        return "默认评估"

def check_crisis_triggers(params: Dict, user_type: str) -> List[str]:
    """检查危机触发条件"""
    triggers = []
    
    # 基础阈值（根据用户类型调整）
    base_thresholds = {
        "积极稳定型": 0.35, "沉稳内敛型": 0.4, "情绪敏感型": 0.45,
        "消极波动型": 0.5, "适应调整型": 0.42
    }
    rsi_threshold = base_thresholds.get(user_type, 0.4)
    
    rsi_value = safe_float(params.get('rsi_value', '1.0'))
    coordination_index = safe_float(params.get('coordination_index', '1.0'))
    eii_value = safe_float(params.get('eii_value', '1.0'))
    
    if rsi_value < rsi_threshold:
        triggers.append("RSI稳定性过低")
    
    if coordination_index < 0.4:
        triggers.append("参数协调性严重失调")
    
    if eii_value < 0.3:
        triggers.append("情绪惯性异常低")
    
    stability_trend = params.get('stability_trend', '')
    if stability_trend in ["稳定下降", "急剧下降"]:
        triggers.append("稳定性持续下降")
    
    # 多维度稳定性检查
    s_stability = safe_float(params.get('s_stability_factor', '1.0'))
    m_stability = safe_float(params.get('m_stability_factor', '1.0'))
    t_stability = safe_float(params.get('t_stability_factor', '1.0'))
    
    low_factors = sum(1 for factor in [s_stability, m_stability, t_stability] if factor < 0.4)
    if low_factors >= 2:
        triggers.append("多维度稳定性异常")
    
    return triggers

def calculate_risk_factors(params: Dict, user_type: str) -> Dict:
    """计算风险因子"""
    type_coefficient = USER_TYPE_COEFFICIENTS.get(user_type, 1.0)
    
    s_stability = safe_float(params.get('s_stability_factor', '1.0'))
    m_stability = safe_float(params.get('m_stability_factor', '1.0'))
    t_stability = safe_float(params.get('t_stability_factor', '1.0'))
    
    s_risk = min(1.0, max(0, 0.5 - s_stability) * 2 * type_coefficient)
    m_risk = min(1.0, max(0, 0.5 - m_stability) * 2 * type_coefficient)
    t_risk = min(1.0, max(0, 0.5 - t_stability) * 2 * type_coefficient)
    
    return {
        "s_risk_factor": s_risk,
        "m_risk_factor": m_risk,
        "t_risk_factor": t_risk
    }

def calculate_crisis_probability(risk_factors: Dict, params: Dict, user_type: str) -> float:
    """计算危机概率"""
    # 基础危机概率
    base_prob = (
        risk_factors['s_risk_factor'] * 0.4 +
        risk_factors['m_risk_factor'] * 0.35 +
        risk_factors['t_risk_factor'] * 0.25
    )
    
    # RSI修正
    rsi_value = safe_float(params.get('rsi_value', '1.0'))
    if rsi_value < 0.2:
        rsi_correction = 1.5
    elif rsi_value < 0.4:
        rsi_correction = 1.2
    else:
        rsi_correction = 1.0
    
    # EII修正
    eii_value = safe_float(params.get('eii_value', '1.0'))
    if eii_value < 0.3:
        eii_correction = 1.3
    elif eii_value > 0.8:
        eii_correction = 0.8
    else:
        eii_correction = 1.0
    
    # 用户类型校正
    type_correction = USER_TYPE_COEFFICIENTS.get(user_type, 1.0)
    
    # 趋势修正
    stability_trend = params.get('stability_trend', '')
    if stability_trend == "急剧下降":
        trend_correction = 1.5
    elif stability_trend == "稳定下降":
        trend_correction = 1.3
    else:
        trend_correction = 1.0
    
    # 最终概率
    crisis_probability = base_prob * rsi_correction * eii_correction * type_correction * trend_correction
    return min(1.0, max(0.0, crisis_probability))

def determine_risk_level(crisis_probability: float) -> Dict:
    """确定风险等级"""
    for level_info in RISK_LEVELS:
        if crisis_probability >= level_info['min']:
            return level_info
    return RISK_LEVELS[-1]  # 默认安全级别

def generate_crisis_context(risk_level: str, triggers: List[str], user_type: str) -> Dict:
    """生成危机上下文信息（供策略匹配树使用）"""
    # 紧急程度映射
    urgency_mapping = {
        "极高风险": 5, "高风险": 4, "中风险": 3, "低风险": 2, "安全": 1
    }

    # 用户脆弱性评估
    vulnerability_mapping = {
        "积极稳定型": {"level": "低", "score": 0.2},
        "沉稳内敛型": {"level": "中低", "score": 0.3},
        "情绪敏感型": {"level": "高", "score": 0.8},
        "消极波动型": {"level": "极高", "score": 0.9},
        "适应调整型": {"level": "中", "score": 0.5}
    }

    # 干预优先级映射
    priority_mapping = {
        "极高风险": "P0-立即响应",
        "高风险": "P1-优先处理",
        "中风险": "P2-及时关注",
        "低风险": "P3-定期监控",
        "安全": "P4-常规维护"
    }

    return {
        "urgency_level": urgency_mapping.get(risk_level, 1),
        "risk_characteristics": {
            "primary_triggers": triggers[:3],
            "trigger_count": len(triggers),
            "complexity_level": "复杂" if len(triggers) > 3 else "简单"
        },
        "user_vulnerability": vulnerability_mapping.get(user_type, {"level": "中", "score": 0.5}),
        "intervention_priority": priority_mapping.get(risk_level, "P3-定期监控")
    }

async def main(args) -> Dict:
    """
    计算6：危机分数计算模块主函数

    输入参数：
    - rsi_value: RSI关系稳定指数
    - confidence_level: 综合置信度等级
    - stability_trend: 稳定性趋势
    - s_stability_factor: S参数稳定性因子
    - m_stability_factor: M参数稳定性因子
    - t_stability_factor: T参数稳定性因子
    - coordination_index: 协调性指数
    - eii_value: EII情绪惯性指数
    - eii_confidence: EII置信度（来自计算5的l3_output_confidence）
    - user_type: 用户类型
    """
    start_time = time.time()
    
    try:
        # 获取输入参数
        params = args.params
        
        # 1. 计算综合置信度和确定评估模式
        confidence = calculate_comprehensive_confidence(params)
        assessment_mode = determine_assessment_mode(confidence)
        
        # 2. 获取用户类型
        user_type = params.get('user_type', '沉稳内敛型')
        
        # 3. 检查危机触发条件
        triggers = check_crisis_triggers(params, user_type)
        
        # 4. 计算风险因子
        risk_factors = calculate_risk_factors(params, user_type)
        
        # 5. 计算危机概率
        crisis_probability = calculate_crisis_probability(risk_factors, params, user_type)
        
        # 6. 确定风险等级
        risk_level_info = determine_risk_level(crisis_probability)
        
        # 7. 生成危机上下文信息
        crisis_context = generate_crisis_context(risk_level_info['level'], triggers, user_type)
        
        # 8. 计算处理时间
        processing_time = round((time.time() - start_time) * 1000, 2)
        
        # 9. 构建输出结果
        ret = {
            "calculation_id": f"crisis_calc_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "calculation_type": "crisis_assessment",
            "version": "6.2.0",
            "timestamp": datetime.now().isoformat(),
            
            "crisis_assessment": {
                "crisis_probability": round(crisis_probability, 3),
                "risk_level": risk_level_info['level'],
                "warning_level": risk_level_info['warning'],
                "crisis_trend": "持续恶化" if crisis_probability > 0.6 else "相对稳定",
                "intervention_urgency": risk_level_info['urgency']
            },
            
            "risk_analysis": {
                "s_risk_factor": round(risk_factors['s_risk_factor'], 3),
                "m_risk_factor": round(risk_factors['m_risk_factor'], 3),
                "t_risk_factor": round(risk_factors['t_risk_factor'], 3),
                "comprehensive_risk": round(
                    risk_factors['s_risk_factor'] * 0.4 +
                    risk_factors['m_risk_factor'] * 0.35 +
                    risk_factors['t_risk_factor'] * 0.25, 3
                ),
                "primary_risk_sources": [
                    source for source, factor in [
                        ("情绪稳定性风险", risk_factors['s_risk_factor']),
                        ("投入度风险", risk_factors['m_risk_factor']),
                        ("时间模式风险", risk_factors['t_risk_factor'])
                    ] if factor > 0.5
                ],
                "risk_trend": "上升" if crisis_probability > 0.5 else "稳定"
            },
            
            "warning_details": {
                "warning_triggers": triggers,
                "intervention_timing": "立即干预" if crisis_probability > 0.6 else "适时关注",
                "expected_escalation": "24小时内可能恶化" if crisis_probability >= 0.8 else "48小时内需要关注",
                "critical_factors": ["情绪稳定性", "关系投入度", "时间管理"]
            },
            
            "crisis_context": crisis_context,
            
            "confidence_breakdown": {
                "l1_input_quality": round(confidence, 3),
                "l2_calculation_stability": 0.9,
                "l3_output_confidence": round(confidence * 0.9, 3)
            },
            
            "validation_result": {
                "probability_validation": 0.0 <= crisis_probability <= 1.0,
                "factor_consistency": True,
                "warning_level_match": True,
                "performance_check": processing_time <= 60
            },
            
            "metadata": {
                "assessment_basis": "RSI稳定性分析+EII惯性评估",
                "assessment_mode": assessment_mode,
                "user_type": user_type,
                "processing_time_ms": processing_time,
                "unique_output": "危机风险评估"
            }
        }
        
        return ret
        
    except Exception as e:
        # 错误处理：返回默认安全评估
        return {
            "calculation_id": f"crisis_calc_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "calculation_type": "crisis_assessment",
            "version": "6.2.0",
            "error_info": {
                "error_occurred": True,
                "error_message": str(e),
                "fallback_mode": "默认安全评估"
            },
            "crisis_assessment": {
                "crisis_probability": 0.3,
                "risk_level": "中风险",
                "warning_level": "黄色预警",
                "intervention_urgency": "预防性关注"
            },
            "metadata": {
                "assessment_mode": "错误降级模式",
                "processing_time_ms": round((time.time() - start_time) * 1000, 2),
                "unique_output": "危机风险评估"
            }
        }
