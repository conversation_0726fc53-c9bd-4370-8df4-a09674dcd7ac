import json
import math
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
from dataclasses import dataclass
import numpy as np

# 定义输入输出类型 - 符合平台要求
class Args:
    def __init__(self, params: Dict[str, Any]):
        self.params = params

Output = Dict[str, Any]

class QualityLevel(Enum):
    """数据质量等级枚举"""
    A = "A"  # 8.0-10.0 优先选取
    B = "B"  # 6.0-7.9 正常选取
    C = "C"  # 4.0-5.9 容量不足时选取
    D = "D"  # <4.0 直接舍弃

@dataclass
class EmotionData:
    """情绪数据结构 - 基于实际数据格式的S/M/T三维模型"""
    bstudio_create_time: str   # T维度：创建时间戳
    conversation: str          # M维度：对话内容（文本表达）
    emo_value: str            # S维度：情绪值（字符串格式）
    number: str               # M维度：字符数量（字符串格式）
    
    # 计算属性
    @property
    def emotion_intensity(self) -> float:
        """获取情绪强度（转换为浮点数）"""
        try:
            return float(self.emo_value)
        except (ValueError, TypeError):
            return 5.0  # 默认中性值
    
    @property
    def expression_text(self) -> str:
        """获取表达文本"""
        return self.conversation or ""
    
    @property
    def expression_length(self) -> int:
        """获取表达长度"""
        try:
            return int(self.number)
        except (ValueError, TypeError):
            return len(self.conversation) if self.conversation else 0
    
    @property
    def expression_complexity(self) -> float:
        """计算表达复杂度（基于文本特征）"""
        if not self.conversation:
            return 0.0
        
        text = self.conversation.strip()
        if len(text) == 0:
            return 0.0
        
        # 简化的复杂度计算：基于字符种类和长度
        unique_chars = len(set(text))
        length_factor = min(len(text) / 10, 3.0)  # 长度因子，最大3分
        diversity_factor = min(unique_chars / 5, 3.0)  # 多样性因子，最大3分
        
        # 检查是否包含标点符号（增加复杂度）
        punctuation_bonus = 1.0 if any(c in text for c in '，。！？；：') else 0.0
        
        return min(length_factor + diversity_factor + punctuation_bonus, 10.0)
    
    @property
    def timestamp(self) -> str:
        """获取时间戳"""
        return self.bstudio_create_time
    
    @property
    def time_context(self) -> str:
        """根据时间戳推断时间上下文"""
        try:
            # 解析时间戳格式："2025-07-03 12:30:31 +0800 CST"
            time_part = self.bstudio_create_time.split(' ')[1]
            hour = int(time_part.split(':')[0])
            
            if 6 <= hour < 12:
                return 'morning'
            elif 12 <= hour < 18:
                return 'afternoon'
            elif 18 <= hour < 22:
                return 'evening'
            else:
                return 'night'
        except:
            return 'unknown'
    
    @property
    def context_info(self) -> dict:
        """上下文信息汇总"""
        return {
            'time_context': self.time_context,
            'expression_length': self.expression_length,
            'expression_complexity': self.expression_complexity,
            'emotion_type': self.emotion_type,
            'user_id': self.user_id,
            'session_id': self.session_id
        }
    
    @property
    def user_id(self) -> str:
        """生成用户ID（基于数据特征）"""
        # 由于原始数据没有用户ID，使用时间戳的日期部分作为会话标识
        try:
            date_part = self.bstudio_create_time.split(' ')[0]
            return f"user_{date_part.replace('-', '')}"
        except:
            return "user_unknown"
    
    @property
    def session_id(self) -> str:
        """生成会话ID（基于时间戳）"""
        try:
            # 使用时间戳的小时部分作为会话分组
            datetime_part = self.bstudio_create_time.split(' +')[0]
            return f"session_{datetime_part.replace(' ', '_').replace(':', '').replace('-', '')}"
        except:
            return "session_unknown"
    
    @property
    def emotion_type(self) -> str:
        """根据情绪值推断情绪类型"""
        intensity = self.emotion_intensity
        if intensity >= 8:
            return 'joy'  # 高兴
        elif intensity >= 6:
            return 'satisfaction'  # 满意
        elif intensity <= 3:
            return 'sadness'  # 难过
        elif intensity <= 4:
            return 'dissatisfaction'  # 不满
        else:
            return 'neutral'  # 中性

@dataclass
class QualityScore:
    """质量评分结果"""
    completeness_score: float    # 完整性得分
    consistency_score: float     # 一致性得分
    dqs_score: float            # DQS总分
    quality_level: str          # 质量等级
    weight_coefficient: float    # 权重系数
    detailed_scores: Dict[str, float]  # 详细评分
    issues: List[str]           # 发现的问题

class DataQualityValidator:
    """数据质量验证器 - 1.1.2节实现
    
    基于心理测量学信度理论的二维质量评分模型：
    - 数据完整性（权重0.6）：核心字段、时间戳、内容丰富度、上下文
    - 行为一致性（权重0.4）：情绪-表达、时间模式、个体基线
    """
    
    def __init__(self):
        # 权重配置（基于心理测量学信度理论）
        self.completeness_weight = 0.6  # 完整性权重
        self.consistency_weight = 0.4   # 一致性权重
        
        # 质量等级阈值标准
        self.quality_thresholds = {
            "A": (8.0, 10.0, 1.0),   # (min_score, max_score, weight_coeff)
            "B": (6.0, 7.9, 0.8),
            "C": (4.0, 5.9, 0.5),
            "D": (0.0, 3.9, 0.1)
        }
    
    def validate_data_quality(self, data: EmotionData, user_baseline: Optional[Dict] = None) -> QualityScore:
        """验证单个数据的质量
        
        Args:
            data: 情绪数据
            user_baseline: 用户基线数据
            
        Returns:
            QualityScore: 质量评分结果
        """
        # 1. 数据完整性评分
        completeness_score, completeness_details = self._evaluate_completeness(data)
        
        # 2. 行为一致性评分
        consistency_score, consistency_details = self._evaluate_consistency(data, user_baseline)
        
        # 3. 计算DQS总分
        dqs_score = (completeness_score * self.completeness_weight + 
                    consistency_score * self.consistency_weight)
        
        # 4. 确定质量等级和权重系数
        quality_level, weight_coefficient = self._determine_quality_level(dqs_score)
        
        # 5. 收集所有问题
        all_issues = completeness_details.get('issues', []) + consistency_details.get('issues', [])
        
        # 6. 合并详细评分
        detailed_scores = {**completeness_details, **consistency_details}
        
        return QualityScore(
            completeness_score=completeness_score,
            consistency_score=consistency_score,
            dqs_score=dqs_score,
            quality_level=quality_level,
            weight_coefficient=weight_coefficient,
            detailed_scores=detailed_scores,
            issues=all_issues
        )
    
    def _evaluate_completeness(self, data: EmotionData) -> Tuple[float, Dict]:
        """评估数据完整性（权重0.6）
        
        评分维度：
        - 核心字段完整性（权重0.3）：S/M/T维度齐全性
        - 时间戳准确性（权重0.25）：时间格式正确性
        - 内容丰富度（权重0.25）：文本信息量充足性
        - 上下文信息（权重0.2）：情境标签完备性
        """
        issues = []
        details = {}
        
        # 1. 核心字段完整性检查（权重0.3）
        core_fields_score = 10.0
        if not data.emotion_type:
            core_fields_score -= 3.0
            issues.append("缺失情绪类型")
        if data.emotion_intensity is None or data.emotion_intensity < 0:
            core_fields_score -= 3.0
            issues.append("缺失或无效情绪强度")
        if not data.expression_text:
            core_fields_score -= 3.0
            issues.append("缺失表达文本")
        
        details['core_fields_score'] = max(0, core_fields_score)
        
        # 2. 时间戳准确性（权重0.25）
        timestamp_score = 10.0
        if not data.timestamp:
            timestamp_score -= 5.0
            issues.append("缺失时间戳")
        elif not self._is_valid_timestamp(data.timestamp):
            timestamp_score -= 2.0
            issues.append("时间戳格式错误")
        
        details['timestamp_score'] = max(0, timestamp_score)
        
        # 3. 内容丰富度（权重0.25）
        richness_score = 10.0
        if data.expression_length < 10:
            richness_score -= 2.0
            issues.append("表达内容过短")
        if data.expression_complexity < 3.0:
            richness_score -= 1.5
            issues.append("表达复杂度不足")
        
        # 检测可疑文本模式（基于1.1.2节算法）
        if self._detect_text_patterns(data.expression_text):
            richness_score -= 3.0
            issues.append("检测到可疑文本模式（重复字符/测试文本/异常长度）")
        
        details['richness_score'] = max(0, richness_score)
        
        # 4. 上下文信息（权重0.2）
        context_score = 10.0
        if not data.time_context or data.time_context == 'unknown':
            context_score -= 2.0
            issues.append("缺失时间上下文")
        if not data.context_info or len(data.context_info) < 3:
            context_score -= 1.0
            issues.append("缺失上下文信息")
        
        details['context_score'] = max(0, context_score)
        
        # 加权计算完整性总分
        completeness_score = (
            details['core_fields_score'] * 0.3 +
            details['timestamp_score'] * 0.25 +
            details['richness_score'] * 0.25 +
            details['context_score'] * 0.2
        )
        
        details['issues'] = issues
        return max(0, completeness_score), details
    
    def _evaluate_consistency(self, data: EmotionData, user_baseline: Optional[Dict] = None) -> Tuple[float, Dict]:
        """评估行为一致性（权重0.4）
        
        评分维度：
        - 情绪-表达一致性（权重0.4）：情绪分数与文本匹配度
        - 时间模式一致性（权重0.3）：符合个人时间规律
        - 个体基线一致性（权重0.3）：与历史基线对比
        """
        issues = []
        details = {}
        
        # 1. 情绪-表达一致性（权重0.4）
        emotion_consistency_score = 10.0
        emotion_text_deviation = self._calculate_emotion_text_deviation(data)
        if emotion_text_deviation > 3.0:
            emotion_consistency_score -= 2.0
            issues.append(f"情绪与文本表达不一致，偏差值：{emotion_text_deviation:.2f}")
        elif emotion_text_deviation > 1.5:
            emotion_consistency_score -= 1.0
            issues.append(f"情绪与文本表达轻微不一致，偏差值：{emotion_text_deviation:.2f}")
        
        details['emotion_consistency_score'] = max(0, emotion_consistency_score)
        
        # 2. 时间模式一致性（权重0.3）
        time_consistency_score = 10.0
        if data.timestamp and data.time_context:
            try:
                # 简化的时间一致性检查
                hour = int(data.timestamp.split('T')[1].split(':')[0]) if 'T' in data.timestamp else 12
                expected_context = self._get_expected_time_context(hour)
                if expected_context != data.time_context:
                    time_consistency_score -= 1.5
                    issues.append(f"时间上下文不一致：期望{expected_context}，实际{data.time_context}")
            except:
                time_consistency_score -= 1.0
                issues.append("时间解析错误")
        
        details['time_consistency_score'] = max(0, time_consistency_score)
        
        # 3. 个体基线一致性（权重0.3）- 支持冷启动机制
        baseline_consistency_score = 10.0
        if data.emotion_intensity is not None:
            # 获取用户数据量（实际应用中从数据库查询）
            user_data_count = user_baseline.get('data_count', 0) if user_baseline else 0
            baseline_deviation = self._calculate_baseline_deviation(data, user_baseline, user_data_count)
            
            if baseline_deviation > 2.0:  # 超过2个标准差
                baseline_consistency_score -= 1.5
                mode = "冷启动" if user_data_count < 10 else "个体基线"
                issues.append(f"偏离{mode}过大，偏差值：{baseline_deviation:.2f}σ")
            elif baseline_deviation > 1.0:
                baseline_consistency_score -= 0.5
                mode = "冷启动" if user_data_count < 10 else "个体基线"
                issues.append(f"轻微偏离{mode}，偏差值：{baseline_deviation:.2f}σ")
        
        details['baseline_consistency_score'] = max(0, baseline_consistency_score)
        
        # 加权计算一致性总分
        consistency_score = (
            details['emotion_consistency_score'] * 0.4 +
            details['time_consistency_score'] * 0.3 +
            details['baseline_consistency_score'] * 0.3
        )
        
        details['issues'] = issues
        return max(0, consistency_score), details
    
    def _calculate_emotion_text_deviation(self, data: EmotionData) -> float:
        """计算情绪与文本表达的偏差"""
        emotion_keywords = {
            'joy': ['好', '棒', '不错', '很好', '真好', '哈哈', '开心', '高兴', '快乐', '兴奋', '愉快', '满意'],
            'satisfaction': ['好', '不错', '可以', '还行', '满意', '棒', '很好'],
            'sadness': ['难过', '伤心', '沮丧', '失落', '悲伤', '不好', '糟糕', '痛苦'],
            'dissatisfaction': ['不好', '不行', '差', '糟糕', '烦', '讨厌', '不满'],
            'neutral': ['一般', '还好', '普通', '正常', '平常'],
            'anger': ['生气', '愤怒', '恼火', '烦躁', '气愤', '讨厌'],
            'fear': ['害怕', '恐惧', '担心', '焦虑', '紧张'],
            'surprise': ['惊讶', '意外', '震惊', '吃惊', '惊奇']
        }
        
        if data.emotion_type not in emotion_keywords:
            return 1.5  # 未知情绪类型，轻微偏差
        
        keywords = emotion_keywords[data.emotion_type]
        text = data.expression_text
        matches = sum(1 for keyword in keywords if keyword in text)
        match_ratio = matches / len(keywords) if keywords else 0
        
        # 根据匹配度和情绪强度计算偏差
        if match_ratio > 0.2:  # 高匹配度
            return abs(data.emotion_intensity - 7.0) * 0.2
        elif match_ratio > 0.05:  # 中等匹配度
            return abs(data.emotion_intensity - 5.0) * 0.4
        else:  # 低匹配度或无匹配
            # 对于中性词汇，偏差较小
            if data.emotion_type == 'neutral':
                return abs(data.emotion_intensity - 5.0) * 0.3
            else:
                return abs(data.emotion_intensity - 5.0) * 0.6
    
    def _get_expected_time_context(self, hour: int) -> str:
        """根据小时获取期望的时间上下文"""
        if 6 <= hour < 12:
            return 'morning'
        elif 12 <= hour < 18:
            return 'afternoon'
        elif 18 <= hour < 22:
            return 'evening'
        else:
            return 'night'
    
    def _calculate_baseline_deviation(self, data: EmotionData, baseline: Dict, user_data_count: int = 0) -> float:
        """计算与个体基线的偏离程度（支持冷启动机制和P25/P50/P75后验基线）
        
        Args:
            data: 情绪数据对象
            baseline: 用户基线数据，支持两种格式：
                     - 旧格式：{'avg_emotion_intensity': float, 'emotion_intensity_std': float}
                     - 新格式：{'P25': float, 'P50': float, 'P75': float}
            user_data_count: 用户数据量，用于冷启动判断
            
        Returns:
            float: 标准化偏差值
        """
        # 冷启动阈值：当用户数据量少于10条时使用默认基线
        COLD_START_THRESHOLD = 10
        
        if user_data_count < COLD_START_THRESHOLD:
            # 冷启动阶段：使用理论默认基线（稳定型用户模式）
            P25, P50, P75 = 4.0, 5.0, 6.0
            baseline_std = (P75 - P25) / 1.35  # 基于分位数估算标准差
            baseline_intensity = P50
        else:
            # 正常阶段：使用个体历史数据基线
            if baseline:
                # 优先使用新的P25/P50/P75格式
                if 'P25' in baseline and 'P50' in baseline and 'P75' in baseline:
                    P25 = baseline.get('P25', 4.0)
                    P50 = baseline.get('P50', 5.0) 
                    P75 = baseline.get('P75', 6.0)
                    baseline_std = max(0.5, (P75 - P25) / 1.35)  # 确保最小标准差
                    baseline_intensity = P50
                else:
                    # 兼容旧格式
                    baseline_intensity = baseline.get('avg_emotion_intensity', 5.0)
                    baseline_std = baseline.get('emotion_intensity_std', 2.0)
            else:
                # 如果没有基线数据，使用默认值
                baseline_intensity = 5.0
                baseline_std = 2.0
        
        if baseline_std == 0:
            baseline_std = 1.0  # 避免除零
        
        deviation = abs(data.emotion_intensity - baseline_intensity) / baseline_std
        return deviation
    
    def _is_valid_timestamp(self, timestamp: str) -> bool:
        """检查时间戳格式是否有效"""
        try:
            # 简单的时间戳格式检查
            return len(timestamp) > 10 and ('T' in timestamp or ' ' in timestamp)
        except:
            return False
    
    def _detect_text_patterns(self, context: str) -> bool:
        """检测可疑的文本模式
        
        基于1.1.2节数据质量验证体系的文本模式检测算法：
        1. 检测重复字符（如"哈哈哈"、"啊啊啊"）
        2. 检测明显的测试文本
        3. 检测过短或过长的异常文本
        
        Args:
            context: 待检测的文本内容
            
        Returns:
            bool: True表示检测到可疑模式，False表示正常
        """
        if not context or not isinstance(context, str):
            return True  # 空文本或非字符串视为可疑
        
        context = context.strip()
        
        # 1. 检测重复字符或明显的测试文本
        # 字符种类太少且长度较长（如"哈哈哈哈哈"、"啊啊啊啊"）
        if len(set(context)) < 3 and len(context) > 5:
            return True
        
        # 2. 检测明显的测试文本模式
        test_patterns = [
            'test', '测试', 'aaa', '111', 'xxx', '....',
            'aaaa', 'bbbb', 'cccc', '1111', '2222', '3333',
            'abcd', '1234', 'qwer', 'asdf', 'zxcv',
            '呵呵呵', '嘿嘿嘿', '嗯嗯嗯', '额额额',
            'lalala', 'hahaha', 'hehehe', 'hohoho'
        ]
        
        context_lower = context.lower()
        if any(pattern in context_lower for pattern in test_patterns):
            return True
        
        # 3. 检测过短或过长的异常文本
        if len(context) < 2:  # 过短
            return True
        if len(context) > 500:  # 过长
            return True
        
        # 4. 检测纯数字或纯符号
        if context.isdigit() and len(context) > 3:  # 纯数字且较长
            return True
        
        # 5. 检测重复词汇模式（如"好好好好"、"不不不不"）
        if len(context) >= 6:
            # 检查是否为同一个词的重复
            for word_len in [1, 2, 3]:
                if len(context) % word_len == 0:
                    word = context[:word_len]
                    if word * (len(context) // word_len) == context:
                        return True
        
        return False
    
    def _determine_quality_level(self, dqs_score: float) -> Tuple[str, float]:
        """根据DQS分数确定质量等级和权重系数"""
        for level, (min_score, max_score, weight_coeff) in self.quality_thresholds.items():
            if min_score <= dqs_score <= max_score:
                return level, weight_coeff
        return "D", 0.1  # 默认返回D级

# 主函数 - 符合平台要求的async格式
def main(args: Args) -> Output:
    """1.1.2节 数据质量验证体系主函数
    
    输入参数：
    - outputList: 原始情绪数据列表，每条数据可包含基线字段：
        * 基础字段：bstudio_create_time, conversation, emo_value, number
        * 基线字段（可选）：P25, P50, P75, data_count
        * 兼容字段（可选）：avg_emotion_intensity, emotion_intensity_std
    
    示例格式：
    [
        {
            "bstudio_create_time": "2024-01-15 14:30:00",
            "conversation": "今天心情不错",
            "emo_value": "8.5",
            "number": "15",
            "P25": 6.2,      # 可选：25%分位数基线
            "P50": 7.8,      # 可选：50%分位数基线
            "P75": 8.9,      # 可选：75%分位数基线
            "data_count": 25  # 可选：用户历史数据量
        }
    ]
    
    输出结果：
    - validation_results: 验证结果列表
    - statistics: 质量统计信息
    - quality_thresholds: 质量阈值标准
    - algorithm_info: 算法信息
    - processing_summary: 处理摘要（包含模式信息）
    """
    params = args.params
    
    # 获取输入参数
    output_list = params.get('outputList', [])
    
    # 初始化验证器
    validator = DataQualityValidator()
    
    # 处理数据
    results = []
    statistics = {
        'total_count': 0,
        'level_distribution': {'A': 0, 'B': 0, 'C': 0, 'D': 0},
        'level_percentages': {'A': 0.0, 'B': 0.0, 'C': 0.0, 'D': 0.0},
        'average_dqs': 0.0,
        'average_completeness': 0.0,
        'average_consistency': 0.0,
        'quality_rate': {'A_B_rate': 0.0, 'usable_rate': 0.0}
    }
    
    if output_list:
        total_dqs = 0
        total_completeness = 0
        total_consistency = 0
        
        for data_dict in output_list:
            try:
                # 转换为EmotionData对象（使用实际数据格式）
                emotion_data = EmotionData(
                    bstudio_create_time=data_dict.get('bstudio_create_time', ''),
                    conversation=data_dict.get('conversation', ''),
                    emo_value=data_dict.get('emo_value', '5'),
                    number=data_dict.get('number', '0')
                )
                
                # 从当前数据中提取基线信息
                baseline = {}
                if 'P25' in data_dict and 'P50' in data_dict and 'P75' in data_dict:
                    # 使用P25/P50/P75格式
                    baseline = {
                        'P25': float(data_dict.get('P25', 4.0)),
                        'P50': float(data_dict.get('P50', 5.0)),
                        'P75': float(data_dict.get('P75', 6.0)),
                        'data_count': int(data_dict.get('data_count', 0))
                    }
                elif 'avg_emotion_intensity' in data_dict:
                    # 兼容旧格式
                    baseline = {
                        'avg_emotion_intensity': float(data_dict.get('avg_emotion_intensity', 5.0)),
                        'emotion_intensity_std': float(data_dict.get('emotion_intensity_std', 2.0)),
                        'data_count': int(data_dict.get('data_count', 0))
                    }
                else:
                    # 无基线数据，使用空字典（将触发冷启动）
                    baseline = {'data_count': 0}
                
                # 验证质量
                quality_score = validator.validate_data_quality(emotion_data, baseline)
                
                # 获取用户数据量（用于冷启动判断）
                user_data_count = baseline.get('data_count', 0)
                is_cold_start = user_data_count < 10
                
                # 转换为字典格式输出（移除原始输入参数）
                result_dict = {
                    'data_id': emotion_data.session_id,  # 数据标识
                    'user_id': emotion_data.user_id,
                    'emotion_analysis': {
                        'emotion_type': emotion_data.emotion_type,
                        'emotion_intensity': emotion_data.emotion_intensity,
                        'expression_text': emotion_data.expression_text,
                        'expression_length': emotion_data.expression_length,
                        'expression_complexity': round(emotion_data.expression_complexity, 2),
                        'time_context': emotion_data.time_context,
                        'timestamp': emotion_data.timestamp
                    },
                    'quality_assessment': {
                        'dqs_score': round(quality_score.dqs_score, 2),
                        'quality_level': quality_score.quality_level,
                        'weight_coefficient': quality_score.weight_coefficient,
                        'completeness_score': round(quality_score.completeness_score, 2),
                        'consistency_score': round(quality_score.consistency_score, 2),
                        'processing_decision': _get_processing_decision(quality_score.quality_level)
                    },
                    'baseline_info': {
                        'is_cold_start': is_cold_start,
                        'user_data_count': user_data_count,
                        'baseline_mode': '冷启动默认基线' if is_cold_start else '个体历史基线'
                    },
                    'detailed_scores': {
                        k: round(v, 2) if isinstance(v, (int, float)) else v 
                        for k, v in quality_score.detailed_scores.items() 
                        if k != 'issues'
                    },
                    'issues': quality_score.issues
                }
                
                results.append(result_dict)
                
                # 更新统计
                statistics['level_distribution'][quality_score.quality_level] += 1
                total_dqs += quality_score.dqs_score
                total_completeness += quality_score.completeness_score
                total_consistency += quality_score.consistency_score
                
            except Exception as e:
                # 处理数据转换错误
                error_result = {
                    'original_data': data_dict,
                    'error': f"数据处理错误: {str(e)}",
                    'quality_assessment': {
                        'dqs_score': 0.0,
                        'quality_level': 'D',
                        'weight_coefficient': 0.1,
                        'processing_decision': '数据格式错误，直接舍弃'
                    }
                }
                results.append(error_result)
                statistics['level_distribution']['D'] += 1
        
        # 计算统计信息
        count = len(results)
        statistics['total_count'] = count
        
        if count > 0:
            statistics['average_dqs'] = round(total_dqs / count, 2)
            statistics['average_completeness'] = round(total_completeness / count, 2)
            statistics['average_consistency'] = round(total_consistency / count, 2)
            
            # 计算百分比
            for level in ['A', 'B', 'C', 'D']:
                statistics['level_percentages'][level] = round(
                    statistics['level_distribution'][level] / count * 100, 1
                )
            
            # 计算质量率
            a_b_count = statistics['level_distribution']['A'] + statistics['level_distribution']['B']
            usable_count = a_b_count + statistics['level_distribution']['C']
            
            statistics['quality_rate']['A_B_rate'] = round(a_b_count / count * 100, 1)
            statistics['quality_rate']['usable_rate'] = round(usable_count / count * 100, 1)
    
    # 构建输出
    ret: Output = {
        'validation_results': results,
        'statistics': statistics,
        'quality_thresholds': {
            'A_level': '8.0-10.0分，优先选取，权重系数1.0',
            'B_level': '6.0-7.9分，正常选取，权重系数0.8', 
            'C_level': '4.0-5.9分，容量不足时选取，权重系数0.5',
            'D_level': '0.0-3.9分，直接舍弃，权重系数0.1'
        },
        'algorithm_info': {
            'section': '1.1.2 数据质量验证体系',
            'model': '简化的二维质量评分模型',
            'theory_basis': '心理测量学信度理论',
            'completeness_weight': 0.6,
            'consistency_weight': 0.4,
            'evaluation_dimensions': {
                'completeness': ['核心字段完整性', '时间戳准确性', '内容丰富度', '上下文信息'],
                'consistency': ['情绪-表达一致性', '时间模式一致性', '个体基线一致性']
            }
        },
        'processing_summary': {
            'total_processed': len(results),
            'timestamp': datetime.now().isoformat()
        }
    }
    
    return ret

def _get_processing_decision(quality_level: str) -> str:
    """根据质量等级获取处理决策"""
    decisions = {
        'A': '优先选取，直接进入核心存储',
        'B': '正常选取，进入标准存储流程',
        'C': '条件选取，容量不足时使用',
        'D': '直接舍弃，不进入存储系统'
    }
    return decisions.get(quality_level, '未知处理策略')

# 示例使用
if __name__ == "__main__":
    
    # 示例数据 - 包含基线数据的完整格式
    sample_data = [
        {
            "bstudio_create_time": "2024-01-15 14:30:00 +0800 CST",
            "conversation": "今天天气真好，心情很不错！",
            "emo_value": "8.5",
            "number": "15",
            "P25": 6.2,      # 25%分位数基线
            "P50": 7.8,      # 50%分位数基线（中位数）
            "P75": 8.9,      # 75%分位数基线
            "data_count": 25  # 用户历史数据量（>=10为正常模式）
        },
        {
            "bstudio_create_time": "2024-01-15 22:15:00 +0800 CST", 
            "conversation": "工作压力太大了，感觉很累",
            "emo_value": "3.2",
            "number": "12",
            "P25": 4.1,      # 波动型用户示例
            "P50": 6.5,
            "P75": 8.8,
            "data_count": 15
        },
        {
            "bstudio_create_time": "2024-01-15 09:45:00 +0800 CST",
            "conversation": "早上好！准备开始新的一天",
            "emo_value": "7.0",
            "number": "14",
            "data_count": 3  # 冷启动用户示例（数据量<10，将使用默认基线）
        },
        {
            "bstudio_create_time": "2024-01-15 16:20:00 +0800 CST",
            "conversation": "这个产品真的很棒，我很满意",
            "emo_value": "8.8",
            "number": "16",
            "avg_emotion_intensity": 7.0,  # 兼容旧格式示例
            "emotion_intensity_std": 1.5,
            "data_count": 20
        },
        {
            "bstudio_create_time": "2024-01-15 11:10:00 +0800 CST",
            "conversation": "一般般吧，没什么特别的感觉",
            "emo_value": "5.0",
            "number": "15"
            # 无基线数据，将触发冷启动机制
        }
    ]
    
    def demo():
        """演示数据质量验证功能"""
        print("=== 1.1.2节 数据质量验证体系演示 ===")
        print("基于二维质量评分模型(DQS)进行数据质量评估\n")
        
        # 批量验证（基线数据已直接包含在outputList中）
        args = Args({
            'outputList': sample_data
        })
        
        result = main(args)
        
        print("=== 整体质量统计 ===")
        stats = result['statistics']
        print(f"总数据量: {stats['total_count']}")
        print(f"A级数据: {stats['level_distribution']['A']} ({stats['level_percentages']['A']}%)")
        print(f"B级数据: {stats['level_distribution']['B']} ({stats['level_percentages']['B']}%)")
        print(f"C级数据: {stats['level_distribution']['C']} ({stats['level_percentages']['C']}%)")
        print(f"D级数据: {stats['level_distribution']['D']} ({stats['level_percentages']['D']}%)")
        print(f"平均DQS分数: {stats['average_dqs']}")
        print(f"A+B级质量率: {stats['quality_rate']['A_B_rate']}%")
        print(f"可用数据率: {stats['quality_rate']['usable_rate']}%")
        
        print("\n=== 详细质量分析 ===")
        for i, item in enumerate(result['validation_results']):
            print(f"\n--- 数据样本 {i+1} ---")
            
            # 显示情绪分析结果
            if 'emotion_analysis' in item:
                emotion = item['emotion_analysis']
                print(f"文本内容: {emotion.get('expression_text', 'N/A')}")
                print(f"情绪类型: {emotion.get('emotion_type', 'N/A')}")
                print(f"情绪强度: {emotion.get('emotion_intensity', 'N/A')}")
                print(f"表达复杂度: {emotion.get('expression_complexity', 'N/A')}")
                print(f"时间上下文: {emotion.get('time_context', 'N/A')}")
            
            # 显示质量评估结果
            if 'quality_assessment' in item:
                assessment = item['quality_assessment']
                print(f"DQS总分: {assessment['dqs_score']}")
                print(f"质量等级: {assessment['quality_level']}")
                print(f"权重系数: {assessment['weight_coefficient']}")
                print(f"处理决策: {assessment['processing_decision']}")
            
            # 显示基线信息（冷启动机制）
            if 'baseline_info' in item:
                baseline = item['baseline_info']
                print(f"基线模式: {baseline['baseline_mode']}")
                print(f"用户数据量: {baseline['user_data_count']}")
                print(f"冷启动状态: {'是' if baseline['is_cold_start'] else '否'}")
            
            # 显示详细分数
            if 'detailed_scores' in item:
                scores = item['detailed_scores']
                print(f"完整性分数: {scores.get('completeness_score', 'N/A')}")
                print(f"一致性分数: {scores.get('consistency_score', 'N/A')}")
            
            # 显示质量问题
            if 'issues' in item and item['issues']:
                print(f"质量问题: {', '.join(item['issues'])}")
            else:
                print("质量问题: 无")
            
            # 显示错误信息
            if 'error' in item:
                print(f"处理错误: {item['error']}")
        
        print("\n=== 质量分级标准说明 ===")
        thresholds = result['quality_thresholds']
        for level, desc in thresholds.items():
            print(f"{level}: {desc}")
        
        print("\n=== 算法信息 ===")
        algo_info = result['algorithm_info']
        print(f"算法章节: {algo_info['section']}")
        print(f"评分模型: {algo_info['model']}")
        print(f"理论基础: {algo_info['theory_basis']}")
        print(f"完整性权重: {algo_info['completeness_weight']}")
        print(f"一致性权重: {algo_info['consistency_weight']}")
    
    # 运行演示
    demo()