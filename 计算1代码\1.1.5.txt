from datetime import datetime, timedelta
import numpy as np

# 定义记忆层配置
MEMORY_LAYERS = {
    "working_memory": {"days_range": (0, 7), "capacity": 50, "weight": 1.0},
    "short_memory": {"days_range": (8, 28), "capacity": 150, "weight": 0.8},
    "long_memory": {"days_range": (29, 112), "capacity": 300, "weight": 1.0},
    "core_memory": {"days_range": (113, float('inf')), "capacity": 100, "weight": 0.4}
}

# 权重配置
W1, W2, W3, W4, W5, W6 = 0.25, 0.15, 0.1, 0.2, 0.15, 0.15

async def main(args: Args) -> Output:
    # 获取输入参数
    params = args.params
    output_list = params['outputList']
    quality_score = float(params['quality_score'])
    
    # 当前时间
    current_time = datetime.strptime("2025-07-05 21:37:26", "%Y-%m-%d %H:%M:%S")
    
    # 初始化记忆层
    memory_layers = {layer: [] for layer in MEMORY_LAYERS}
    immediate_memory = []
    
    # 用户情绪基线（模拟数据库读取）
    user_baseline = 5.0  # 新用户初始基线
    emotion_data_count = 0  # 用于后续基线更新
    
    # 处理每条输入数据
    for item in output_list:
        # 解析时间
        create_time = datetime.strptime(item["bstudio_create_time"].split()[0], "%Y-%m-%d")
        delta_days = (current_time - create_time).days
        
        # 计算时间相关性
        time_relevance = max(0, 1 - delta_days / 112)  # 最大时间范围112天
        
        # 基础特征提取
        emo_value = float(item["emo_value"])
        word_count = len(item["conversation"])
        interaction_strength = int(item["number"])
        
        # 计算特征重要性得分
        emotion_deviation = abs(emo_value - user_baseline)
        extreme_strength = min(abs(emo_value - 0), abs(emo_value - 10)) / 5
        word_weight = min(word_count / 50, 2.0)
        
        # 情绪变化幅度（假设无历史数据）
        emotion_change = 0  # 需要历史数据支持
        
        # 情绪类型代表性
        if emo_value > 6.5:
            emotion_type_rep = 1.0
        elif emo_value < 3.5:
            emotion_type_rep = 1.2
        else:
            emotion_type_rep = 0.8
            
        # 特征重要性得分
        feature_score = (
            W1 * emotion_deviation +
            W2 * extreme_strength +
            W3 * word_weight +
            W4 * emotion_change +
            W5 * time_relevance +
            W6 * emotion_type_rep
        )
        
        # 计算优先级评分
        priority_score = (
            0.4 * quality_score +
            0.25 * emo_value +
            0.2 * time_relevance +
            0.1 * feature_score +
            0.05 * interaction_strength
        )
        
        # 添加扩展属性
        processed_item = item.copy()
        processed_item.update({
            "delta_days": delta_days,
            "priority_score": priority_score,
            "feature_score": feature_score,
            "emotion_deviation": emotion_deviation
        })
        
        # 分配到对应记忆层
        for layer_name, config in MEMORY_LAYERS.items():
            min_days, max_days = config["days_range"]
            if min_days <= delta_days <= max_days:
                memory_layers[layer_name].append(processed_item)
                break
        
        # 个人特征层检测（示例规则：用户主动分享个人信息）
        if "我是" in item["conversation"] or "我喜欢" in item["conversation"]:
            immediate_memory.append(processed_item)
    
    # 数据淘汰与排序
    for layer_name, config in MEMORY_LAYERS.items():
        # 按优先级排序
        memory_layers[layer_name].sort(key=lambda x: x["priority_score"], reverse=True)
        
        # 容量管理
        capacity = config["capacity"]
        if len(memory_layers[layer_name]) > capacity * 0.9:
            print(f"Warning: {layer_name} 接近容量上限")
        memory_layers[layer_name] = memory_layers[layer_name][:capacity]
    
    # 更新用户基线（增量计算）
    total_emotion = sum(float(item["emo_value"]) for item in output_list)
    new_count = len(output_list)
    updated_baseline = (user_baseline * emotion_data_count + total_emotion) / (emotion_data_count + new_count)
    
    # 构建输出
    ret: Output = {
        "working_memory": memory_layers["working_memory"],
        "short_memory": memory_layers["short_memory"],
        "long_memory": memory_layers["long_memory"],
        "core_memory": memory_layers["core_memory"],
        "immediate_memory": immediate_memory
    }
    
    return ret